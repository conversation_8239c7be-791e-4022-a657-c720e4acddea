use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use std::fmt;
use thiserror::Error;
use tracing::{error, warn};
use uuid::Uuid;

/// Application-wide error type
#[derive(Error, Debug)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),

    #[error("Redis error: {0}")]
    Redis(#[from] redis::RedisError),

    #[error("Validation error: {message}")]
    Validation { message: String },

    #[error("Authentication error: {message}")]
    Authentication { message: String },

    #[error("Authorization error: {message}")]
    Authorization { message: String },

    #[error("Not found: {resource}")]
    NotFound { resource: String },

    #[error("Conflict: {message}")]
    Conflict { message: String },

    #[error("Bad request: {message}")]
    BadRequest { message: String },

    #[error("Internal server error: {message}")]
    Internal { message: String },

    #[error("Service unavailable: {message}")]
    ServiceUnavailable { message: String },

    #[error("Rate limit exceeded")]
    RateLimitExceeded,

    #[error("File error: {message}")]
    File { message: String },

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("JWT error: {0}")]
    Jwt(#[from] jsonwebtoken::errors::Error),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Configuration error: {message}")]
    Configuration { message: String },

    #[error("External service error: {service}: {message}")]
    ExternalService { service: String, message: String },

    #[error("Timeout error: {operation}")]
    Timeout { operation: String },

    #[error("Tenant not found or inactive")]
    TenantNotFound,

    #[error("User not found or inactive")]
    UserNotFound,

    #[error("Permission denied: {permission}")]
    PermissionDenied { permission: String },

    #[error("Account locked until {until}")]
    AccountLocked { until: chrono::DateTime<chrono::Utc> },

    #[error("Invalid credentials")]
    InvalidCredentials,

    #[error("Session expired")]
    SessionExpired,

    #[error("File too large: {size} bytes (max: {max_size} bytes)")]
    FileTooLarge { size: u64, max_size: u64 },

    #[error("Unsupported file type: {file_type}")]
    UnsupportedFileType { file_type: String },

    #[error("Workflow error: {message}")]
    Workflow { message: String },

    #[error("Business rule violation: {rule}")]
    BusinessRule { rule: String },
}

/// Error response structure for API
#[derive(serde::Serialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
    pub code: String,
    pub request_id: Option<String>,
    pub details: Option<serde_json::Value>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub trace_id: Option<String>,
    pub help_url: Option<String>,
    pub retry_after: Option<u64>,
    pub context: Option<ErrorContext>,
}

/// Additional error context for debugging and user assistance
#[derive(serde::Serialize)]
pub struct ErrorContext {
    pub user_id: Option<uuid::Uuid>,
    pub tenant_id: Option<uuid::Uuid>,
    pub operation: Option<String>,
    pub resource: Option<String>,
    pub correlation_id: Option<String>,
    pub stack_trace: Option<String>,
}

impl AppError {
    /// Create a validation error
    pub fn validation(message: impl Into<String>) -> Self {
        Self::Validation {
            message: message.into(),
        }
    }

    /// Create an authentication error
    pub fn authentication(message: impl Into<String>) -> Self {
        Self::Authentication {
            message: message.into(),
        }
    }

    /// Create an authorization error
    pub fn authorization(message: impl Into<String>) -> Self {
        Self::Authorization {
            message: message.into(),
        }
    }

    /// Create a not found error
    pub fn not_found(resource: impl Into<String>) -> Self {
        Self::NotFound {
            resource: resource.into(),
        }
    }

    /// Create a conflict error
    pub fn conflict(message: impl Into<String>) -> Self {
        Self::Conflict {
            message: message.into(),
        }
    }

    /// Create a bad request error
    pub fn bad_request(message: impl Into<String>) -> Self {
        Self::BadRequest {
            message: message.into(),
        }
    }

    /// Create an internal error
    pub fn internal(message: impl Into<String>) -> Self {
        Self::Internal {
            message: message.into(),
        }
    }

    /// Create a file error
    pub fn file_error(message: impl Into<String>) -> Self {
        Self::File {
            message: message.into(),
        }
    }

    /// Create a configuration error
    pub fn configuration(message: impl Into<String>) -> Self {
        Self::Configuration {
            message: message.into(),
        }
    }

    /// Create an external service error
    pub fn external_service(service: impl Into<String>, message: impl Into<String>) -> Self {
        Self::ExternalService {
            service: service.into(),
            message: message.into(),
        }
    }

    /// Create a timeout error
    pub fn timeout(operation: impl Into<String>) -> Self {
        Self::Timeout {
            operation: operation.into(),
        }
    }

    /// Create a permission denied error
    pub fn permission_denied(permission: impl Into<String>) -> Self {
        Self::PermissionDenied {
            permission: permission.into(),
        }
    }

    /// Create an account locked error
    pub fn account_locked(until: chrono::DateTime<chrono::Utc>) -> Self {
        Self::AccountLocked { until }
    }

    /// Create a file too large error
    pub fn file_too_large(size: u64, max_size: u64) -> Self {
        Self::FileTooLarge { size, max_size }
    }

    /// Create an unsupported file type error
    pub fn unsupported_file_type(file_type: impl Into<String>) -> Self {
        Self::UnsupportedFileType {
            file_type: file_type.into(),
        }
    }

    /// Create a workflow error
    pub fn workflow(message: impl Into<String>) -> Self {
        Self::Workflow {
            message: message.into(),
        }
    }

    /// Create a business rule violation error
    pub fn business_rule(rule: impl Into<String>) -> Self {
        Self::BusinessRule {
            rule: rule.into(),
        }
    }

    /// Create a service unavailable error
    pub fn service_unavailable(message: impl Into<String>) -> Self {
        Self::ServiceUnavailable {
            message: message.into(),
        }
    }

    /// Get the HTTP status code for this error
    pub fn status_code(&self) -> StatusCode {
        match self {
            AppError::Validation { .. } => StatusCode::BAD_REQUEST,
            AppError::Authentication { .. } => StatusCode::UNAUTHORIZED,
            AppError::Authorization { .. } => StatusCode::FORBIDDEN,
            AppError::NotFound { .. } => StatusCode::NOT_FOUND,
            AppError::Conflict { .. } => StatusCode::CONFLICT,
            AppError::BadRequest { .. } => StatusCode::BAD_REQUEST,
            AppError::RateLimitExceeded => StatusCode::TOO_MANY_REQUESTS,
            AppError::ServiceUnavailable { .. } => StatusCode::SERVICE_UNAVAILABLE,
            AppError::TenantNotFound => StatusCode::NOT_FOUND,
            AppError::UserNotFound => StatusCode::NOT_FOUND,
            AppError::PermissionDenied { .. } => StatusCode::FORBIDDEN,
            AppError::AccountLocked { .. } => StatusCode::LOCKED,
            AppError::InvalidCredentials => StatusCode::UNAUTHORIZED,
            AppError::SessionExpired => StatusCode::UNAUTHORIZED,
            AppError::FileTooLarge { .. } => StatusCode::PAYLOAD_TOO_LARGE,
            AppError::UnsupportedFileType { .. } => StatusCode::UNSUPPORTED_MEDIA_TYPE,
            AppError::Timeout { .. } => StatusCode::REQUEST_TIMEOUT,
            AppError::BusinessRule { .. } => StatusCode::UNPROCESSABLE_ENTITY,
            AppError::Workflow { .. } => StatusCode::UNPROCESSABLE_ENTITY,
            _ => StatusCode::INTERNAL_SERVER_ERROR,
        }
    }

    /// Get the error code for this error
    pub fn error_code(&self) -> &'static str {
        match self {
            AppError::Database(_) => "DATABASE_ERROR",
            AppError::Redis(_) => "REDIS_ERROR",
            AppError::Validation { .. } => "VALIDATION_ERROR",
            AppError::Authentication { .. } => "AUTHENTICATION_ERROR",
            AppError::Authorization { .. } => "AUTHORIZATION_ERROR",
            AppError::NotFound { .. } => "NOT_FOUND",
            AppError::Conflict { .. } => "CONFLICT",
            AppError::BadRequest { .. } => "BAD_REQUEST",
            AppError::Internal { .. } => "INTERNAL_ERROR",
            AppError::ServiceUnavailable { .. } => "SERVICE_UNAVAILABLE",
            AppError::RateLimitExceeded => "RATE_LIMIT_EXCEEDED",
            AppError::File { .. } => "FILE_ERROR",
            AppError::Serialization(_) => "SERIALIZATION_ERROR",
            AppError::Jwt(_) => "JWT_ERROR",
            AppError::Io(_) => "IO_ERROR",
            AppError::Configuration { .. } => "CONFIGURATION_ERROR",
            AppError::ExternalService { .. } => "EXTERNAL_SERVICE_ERROR",
            AppError::Timeout { .. } => "TIMEOUT_ERROR",
            AppError::TenantNotFound => "TENANT_NOT_FOUND",
            AppError::UserNotFound => "USER_NOT_FOUND",
            AppError::PermissionDenied { .. } => "PERMISSION_DENIED",
            AppError::AccountLocked { .. } => "ACCOUNT_LOCKED",
            AppError::InvalidCredentials => "INVALID_CREDENTIALS",
            AppError::SessionExpired => "SESSION_EXPIRED",
            AppError::FileTooLarge { .. } => "FILE_TOO_LARGE",
            AppError::UnsupportedFileType { .. } => "UNSUPPORTED_FILE_TYPE",
            AppError::Workflow { .. } => "WORKFLOW_ERROR",
            AppError::BusinessRule { .. } => "BUSINESS_RULE_VIOLATION",
        }
    }

    /// Check if this error should be logged as an error (vs warning/info)
    pub fn should_log_as_error(&self) -> bool {
        matches!(
            self,
            AppError::Database(_)
                | AppError::Redis(_)
                | AppError::Internal { .. }
                | AppError::ServiceUnavailable { .. }
                | AppError::Serialization(_)
                | AppError::Io(_)
                | AppError::Configuration { .. }
                | AppError::ExternalService { .. }
        )
    }

    /// Get user-friendly error message
    pub fn get_user_friendly_message(&self) -> String {
        match self {
            AppError::Validation { .. } => "The provided data is invalid. Please check your input and try again.".to_string(),
            AppError::Authentication { .. } => "Authentication failed. Please check your credentials and try again.".to_string(),
            AppError::Authorization { .. } => "You don't have permission to perform this action.".to_string(),
            AppError::NotFound { resource } => format!("The requested {} was not found.", resource),
            AppError::Conflict { .. } => "This action conflicts with existing data. Please refresh and try again.".to_string(),
            AppError::BadRequest { .. } => "The request is invalid. Please check your input and try again.".to_string(),
            AppError::RateLimitExceeded => "Too many requests. Please wait a moment and try again.".to_string(),
            AppError::TenantNotFound => "Your organization was not found. Please contact support.".to_string(),
            AppError::UserNotFound => "User account not found. Please contact support.".to_string(),
            AppError::PermissionDenied { .. } => "You don't have permission to perform this action.".to_string(),
            AppError::AccountLocked { until } => format!("Your account is locked until {}. Please try again later.", until.format("%Y-%m-%d %H:%M UTC")),
            AppError::InvalidCredentials => "Invalid username or password. Please try again.".to_string(),
            AppError::SessionExpired => "Your session has expired. Please log in again.".to_string(),
            AppError::FileTooLarge { max_size, .. } => format!("File is too large. Maximum size allowed is {} bytes.", max_size),
            AppError::UnsupportedFileType { .. } => "This file type is not supported. Please use a different format.".to_string(),
            AppError::Workflow { .. } => "There was an issue with the workflow. Please try again or contact support.".to_string(),
            AppError::BusinessRule { .. } => "This action violates business rules. Please review and try again.".to_string(),
            AppError::ServiceUnavailable { .. } => "The service is temporarily unavailable. Please try again later.".to_string(),
            AppError::Timeout { .. } => "The operation timed out. Please try again.".to_string(),
            AppError::ExternalService { service, .. } => format!("External service {} is unavailable. Please try again later.", service),
            _ => "An unexpected error occurred. Please try again or contact support if the problem persists.".to_string(),
        }
    }

    /// Get additional error details for debugging
    pub fn get_error_details(&self) -> Option<serde_json::Value> {
        match self {
            AppError::Validation { message } => Some(serde_json::json!({
                "validation_errors": [message]
            })),
            AppError::FileTooLarge { size, max_size } => Some(serde_json::json!({
                "file_size": size,
                "max_size": max_size,
                "size_mb": *size as f64 / (1024.0 * 1024.0),
                "max_size_mb": *max_size as f64 / (1024.0 * 1024.0)
            })),
            AppError::AccountLocked { until } => Some(serde_json::json!({
                "locked_until": until.to_rfc3339(),
                "locked_until_timestamp": until.timestamp()
            })),
            AppError::ExternalService { service, message } => Some(serde_json::json!({
                "service": service,
                "service_error": message
            })),
            _ => None,
        }
    }

    /// Get help URL for this error type
    pub fn get_help_url(&self) -> Option<String> {
        let base_url = std::env::var("HELP_BASE_URL").unwrap_or_else(|_| "https://docs.forms-system.com/errors".to_string());

        match self {
            AppError::Authentication { .. } => Some(format!("{}/authentication", base_url)),
            AppError::Authorization { .. } => Some(format!("{}/permissions", base_url)),
            AppError::FileTooLarge { .. } => Some(format!("{}/file-upload", base_url)),
            AppError::UnsupportedFileType { .. } => Some(format!("{}/file-types", base_url)),
            AppError::RateLimitExceeded => Some(format!("{}/rate-limits", base_url)),
            _ => None,
        }
    }

    /// Get retry-after seconds for retryable errors
    pub fn get_retry_after(&self) -> Option<u64> {
        match self {
            AppError::RateLimitExceeded => Some(60), // 1 minute
            AppError::ServiceUnavailable { .. } => Some(300), // 5 minutes
            AppError::Timeout { .. } => Some(30), // 30 seconds
            AppError::ExternalService { .. } => Some(120), // 2 minutes
            _ => None,
        }
    }

    /// Check if this error is retryable
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            AppError::ServiceUnavailable { .. }
                | AppError::Timeout { .. }
                | AppError::ExternalService { .. }
                | AppError::Database(_) // Some database errors are retryable
                | AppError::Redis(_)
        )
    }
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let status_code = self.status_code();

        // Generate unique error ID for tracking
        let error_id = uuid::Uuid::new_v4().to_string();

        // Log error with appropriate level
        if self.should_log_as_error() {
            error!(
                error_id = %error_id,
                error_code = %self.error_code(),
                status_code = %status_code.as_u16(),
                "Application error: {}",
                self
            );
        } else {
            warn!(
                error_id = %error_id,
                error_code = %self.error_code(),
                status_code = %status_code.as_u16(),
                "Application warning: {}",
                self
            );
        }

        // Create user-friendly message
        let user_message = self.get_user_friendly_message();

        // Determine if we should include stack trace (only in development)
        let include_debug_info = std::env::var("RUST_ENV").unwrap_or_default() != "production";

        let error_response = ErrorResponse {
            error: self.error_code().to_string(),
            message: user_message,
            code: self.error_code().to_string(),
            request_id: Some(error_id),
            details: self.get_error_details(),
            timestamp: chrono::Utc::now(),
            trace_id: None, // Could be extracted from tracing context
            help_url: self.get_help_url(),
            retry_after: self.get_retry_after(),
            context: if include_debug_info {
                Some(ErrorContext {
                    user_id: None, // Could be extracted from request context
                    tenant_id: None, // Could be extracted from request context
                    operation: None,
                    resource: None,
                    correlation_id: None,
                    stack_trace: if include_debug_info {
                        Some(format!("{:?}", self))
                    } else {
                        None
                    },
                })
            } else {
                None
            },
        };

        (status_code, Json(error_response)).into_response()
    }
}

/// Result type alias for application operations
pub type AppResult<T> = Result<T, AppError>;

/// Convert from anyhow::Error to AppError
impl From<anyhow::Error> for AppError {
    fn from(err: anyhow::Error) -> Self {
        AppError::Internal {
            message: err.to_string(),
        }
    }
}

/// Convert from Box<dyn std::error::Error> to AppError
impl From<Box<dyn std::error::Error>> for AppError {
    fn from(err: Box<dyn std::error::Error>) -> Self {
        AppError::Internal {
            message: err.to_string(),
        }
    }
}

/// Utility function to create a validation error from validator errors
#[cfg(feature = "validator")]
pub fn validation_error_from_validator(errors: validator::ValidationErrors) -> AppError {
    let messages: Vec<String> = errors
        .field_errors()
        .iter()
        .flat_map(|(field, errors)| {
            errors.iter().map(move |error| {
                format!(
                    "{}: {}",
                    field,
                    error.message.as_ref().unwrap_or(&"Invalid value".into())
                )
            })
        })
        .collect();

    AppError::validation(messages.join(", "))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_status_codes() {
        assert_eq!(AppError::validation("test").status_code(), StatusCode::BAD_REQUEST);
        assert_eq!(AppError::authentication("test").status_code(), StatusCode::UNAUTHORIZED);
        assert_eq!(AppError::authorization("test").status_code(), StatusCode::FORBIDDEN);
        assert_eq!(AppError::not_found("test").status_code(), StatusCode::NOT_FOUND);
        assert_eq!(AppError::internal("test").status_code(), StatusCode::INTERNAL_SERVER_ERROR);
    }

    #[test]
    fn test_error_codes() {
        assert_eq!(AppError::validation("test").error_code(), "VALIDATION_ERROR");
        assert_eq!(AppError::authentication("test").error_code(), "AUTHENTICATION_ERROR");
        assert_eq!(AppError::authorization("test").error_code(), "AUTHORIZATION_ERROR");
        assert_eq!(AppError::not_found("test").error_code(), "NOT_FOUND");
        assert_eq!(AppError::internal("test").error_code(), "INTERNAL_ERROR");
    }

    #[test]
    fn test_should_log_as_error() {
        assert!(AppError::internal("test").should_log_as_error());
        assert!(AppError::Database(sqlx::Error::RowNotFound).should_log_as_error());
        assert!(!AppError::validation("test").should_log_as_error());
        assert!(!AppError::not_found("test").should_log_as_error());
    }
}
