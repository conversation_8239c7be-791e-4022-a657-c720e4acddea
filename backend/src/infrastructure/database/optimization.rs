use std::sync::Arc;
use sqlx::PgPool;
use tracing::{info, warn, error};
use tokio::time::{interval, Duration};

use crate::infrastructure::database::performance::{DatabasePerformanceMonitor, DatabaseMetrics};
use crate::infrastructure::database::query_builder::IndexOptimizer;

/// Database optimization service
pub struct DatabaseOptimizationService {
    pool: PgPool,
    performance_monitor: DatabasePerformanceMonitor,
    optimization_interval: Duration,
}

impl DatabaseOptimizationService {
    pub fn new(pool: PgPool, slow_query_threshold_ms: u64) -> Self {
        let performance_monitor = DatabasePerformanceMonitor::new(pool.clone(), slow_query_threshold_ms);
        
        Self {
            pool,
            performance_monitor,
            optimization_interval: Duration::from_secs(3600), // 1 hour
        }
    }

    /// Start the optimization service as a background task
    pub fn start_optimization_service(self: Arc<Self>) {
        let service = self.clone();
        tokio::spawn(async move {
            let mut interval_timer = interval(service.optimization_interval);
            
            loop {
                interval_timer.tick().await;
                
                if let Err(e) = service.run_optimization_cycle().await {
                    error!("Database optimization cycle failed: {}", e);
                }
            }
        });
        
        info!("Database optimization service started");
    }

    /// Run a complete optimization cycle
    async fn run_optimization_cycle(&self) -> Result<(), Box<dyn std::error::Error>> {
        info!("Starting database optimization cycle");
        
        // Collect performance metrics
        let metrics = self.performance_monitor.get_metrics().await?;
        self.log_performance_metrics(&metrics).await;
        
        // Analyze and suggest optimizations
        let suggestions = self.performance_monitor.analyze_query_performance().await?;
        if !suggestions.is_empty() {
            info!("Database optimization suggestions: {:#?}", suggestions);
        }
        
        // Auto-optimize if conditions are met
        self.auto_optimize(&metrics).await?;
        
        // Clean up old performance data
        self.cleanup_old_performance_data().await?;
        
        info!("Database optimization cycle completed");
        Ok(())
    }

    /// Log performance metrics for monitoring
    async fn log_performance_metrics(&self, metrics: &DatabaseMetrics) {
        info!(
            "Database Performance Metrics - Connections: {}/{}, Cache Hit Ratio: {:.1}%, Slow Queries: {}",
            metrics.active_connections,
            metrics.total_connections,
            metrics.cache_hit_ratio,
            metrics.slow_queries_count
        );

        // Log warnings for concerning metrics
        if metrics.cache_hit_ratio < 95.0 {
            warn!("Low cache hit ratio: {:.1}% (should be >95%)", metrics.cache_hit_ratio);
        }

        if metrics.slow_queries_count > 10 {
            warn!("High number of slow queries: {}", metrics.slow_queries_count);
        }

        if metrics.active_connections as f64 / metrics.total_connections as f64 > 0.8 {
            warn!("High connection pool utilization: {}/{}", 
                  metrics.active_connections, metrics.total_connections);
        }

        // Record metrics to database for historical tracking
        if let Err(e) = self.performance_monitor.record_metrics(None).await {
            error!("Failed to record performance metrics: {}", e);
        }
    }

    /// Automatically apply optimizations based on metrics
    async fn auto_optimize(&self, metrics: &DatabaseMetrics) -> Result<(), Box<dyn std::error::Error>> {
        // Auto-create missing indexes if slow queries are detected
        if metrics.slow_queries_count > 5 {
            info!("High number of slow queries detected, checking for missing indexes");
            self.create_recommended_indexes().await?;
        }

        // Auto-vacuum if tables are getting large
        if self.should_run_vacuum(metrics).await? {
            info!("Running automatic VACUUM ANALYZE");
            self.run_vacuum_analyze().await?;
        }

        // Update table statistics if needed
        if metrics.slow_queries_count > 0 {
            info!("Updating table statistics");
            self.update_table_statistics().await?;
        }

        Ok(())
    }

    /// Create recommended indexes
    async fn create_recommended_indexes(&self) -> Result<(), Box<dyn std::error::Error>> {
        let recommended_indexes = IndexOptimizer::get_recommended_indexes();
        let partial_indexes = IndexOptimizer::get_partial_indexes();
        
        let all_indexes = [recommended_indexes, partial_indexes].concat();
        
        for index_sql in all_indexes {
            match sqlx::query(&index_sql).execute(&self.pool).await {
                Ok(_) => {
                    info!("Successfully created index: {}", index_sql);
                }
                Err(e) => {
                    // Don't fail the whole process if one index creation fails
                    warn!("Failed to create index '{}': {}", index_sql, e);
                }
            }
        }
        
        Ok(())
    }

    /// Check if VACUUM should be run
    async fn should_run_vacuum(&self, _metrics: &DatabaseMetrics) -> Result<bool, Box<dyn std::error::Error>> {
        // Check if any tables have high dead tuple ratio
        let dead_tuple_ratio: Option<f64> = sqlx::query_scalar(
            r#"
            SELECT MAX(
                CASE 
                    WHEN n_tup_ins + n_tup_upd + n_tup_del = 0 THEN 0
                    ELSE (n_tup_del + n_tup_upd)::float / (n_tup_ins + n_tup_upd + n_tup_del)
                END
            )
            FROM pg_stat_user_tables
            "#
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(dead_tuple_ratio.unwrap_or(0.0) > 0.2) // Run vacuum if >20% dead tuples
    }

    /// Run VACUUM ANALYZE on all tables
    async fn run_vacuum_analyze(&self) -> Result<(), Box<dyn std::error::Error>> {
        // Get all user tables
        let tables: Vec<String> = sqlx::query_scalar(
            "SELECT schemaname||'.'||tablename FROM pg_stat_user_tables"
        )
        .fetch_all(&self.pool)
        .await?;

        for table in tables {
            let vacuum_sql = format!("VACUUM ANALYZE {}", table);
            match sqlx::query(&vacuum_sql).execute(&self.pool).await {
                Ok(_) => {
                    info!("Successfully vacuumed table: {}", table);
                }
                Err(e) => {
                    warn!("Failed to vacuum table '{}': {}", table, e);
                }
            }
        }

        Ok(())
    }

    /// Update table statistics
    async fn update_table_statistics(&self) -> Result<(), Box<dyn std::error::Error>> {
        sqlx::query("ANALYZE").execute(&self.pool).await?;
        info!("Table statistics updated");
        Ok(())
    }

    /// Clean up old performance data
    async fn cleanup_old_performance_data(&self) -> Result<(), Box<dyn std::error::Error>> {
        // Clean up old performance metrics (keep last 30 days)
        let deleted_count: u64 = sqlx::query_scalar(
            "DELETE FROM performance_metrics WHERE recorded_at < NOW() - INTERVAL '30 days'"
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(0);

        if deleted_count > 0 {
            info!("Cleaned up {} old performance metric records", deleted_count);
        }

        // Reset pg_stat_statements if it gets too large
        let statements_count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM pg_stat_statements"
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(0);

        if statements_count > 10000 {
            info!("Resetting pg_stat_statements (had {} entries)", statements_count);
            sqlx::query("SELECT pg_stat_statements_reset()")
                .execute(&self.pool)
                .await?;
        }

        Ok(())
    }

    /// Get current database health status
    pub async fn get_health_status(&self) -> Result<DatabaseHealthStatus, Box<dyn std::error::Error>> {
        let metrics = self.performance_monitor.get_metrics().await?;
        
        let mut issues = Vec::new();
        let mut warnings = Vec::new();
        
        // Check for critical issues
        if metrics.cache_hit_ratio < 90.0 {
            issues.push(format!("Critical: Cache hit ratio is {:.1}% (should be >95%)", metrics.cache_hit_ratio));
        } else if metrics.cache_hit_ratio < 95.0 {
            warnings.push(format!("Warning: Cache hit ratio is {:.1}% (should be >95%)", metrics.cache_hit_ratio));
        }
        
        if metrics.slow_queries_count > 20 {
            issues.push(format!("Critical: {} slow queries detected", metrics.slow_queries_count));
        } else if metrics.slow_queries_count > 5 {
            warnings.push(format!("Warning: {} slow queries detected", metrics.slow_queries_count));
        }
        
        let connection_utilization = metrics.active_connections as f64 / metrics.total_connections as f64;
        if connection_utilization > 0.9 {
            issues.push(format!("Critical: Connection pool utilization is {:.1}%", connection_utilization * 100.0));
        } else if connection_utilization > 0.8 {
            warnings.push(format!("Warning: Connection pool utilization is {:.1}%", connection_utilization * 100.0));
        }
        
        let status = if !issues.is_empty() {
            "critical"
        } else if !warnings.is_empty() {
            "warning"
        } else {
            "healthy"
        };
        
        Ok(DatabaseHealthStatus {
            status: status.to_string(),
            metrics,
            issues,
            warnings,
        })
    }

    /// Force run optimization cycle (for manual triggering)
    pub async fn force_optimization(&self) -> Result<(), Box<dyn std::error::Error>> {
        info!("Manually triggered database optimization");
        self.run_optimization_cycle().await
    }

    /// Get access to the performance monitor
    pub fn performance_monitor(&self) -> &DatabasePerformanceMonitor {
        &self.performance_monitor
    }
}

#[derive(Debug, Clone)]
pub struct DatabaseHealthStatus {
    pub status: String,
    pub metrics: DatabaseMetrics,
    pub issues: Vec<String>,
    pub warnings: Vec<String>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_health_status_classification() {
        // This would require a test database setup
        // For now, just test that the struct can be created
        let metrics = DatabaseMetrics {
            active_connections: 10,
            idle_connections: 5,
            total_connections: 20,
            slow_queries_count: 0,
            average_query_time_ms: 5.0,
            cache_hit_ratio: 98.5,
            deadlocks_count: 0,
            table_sizes: vec![],
            index_usage: vec![],
        };
        
        let status = DatabaseHealthStatus {
            status: "healthy".to_string(),
            metrics,
            issues: vec![],
            warnings: vec![],
        };
        
        assert_eq!(status.status, "healthy");
    }
}
