use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use axum::{
    extract::{Request, State},
    http::{HeaderMap, HeaderValue, StatusCode},
    middleware::Next,
    response::Response,
};
use tracing::{warn, debug};

/// Rate limiter configuration
#[derive(Debug, Clone)]
pub struct RateLimitConfig {
    /// Maximum number of requests per window
    pub max_requests: u32,
    /// Time window duration
    pub window_duration: Duration,
    /// Whether to use IP-based limiting
    pub per_ip: bool,
    /// Whether to use user-based limiting (requires authentication)
    pub per_user: bool,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            max_requests: 100,
            window_duration: Duration::from_secs(60), // 1 minute
            per_ip: true,
            per_user: false,
        }
    }
}

/// Rate limit bucket for tracking requests
#[derive(Debug, <PERSON><PERSON>)]
struct RateLimitBucket {
    count: u32,
    window_start: Instant,
}

impl RateLimitBucket {
    fn new() -> Self {
        Self {
            count: 0,
            window_start: Instant::now(),
        }
    }

    fn is_expired(&self, window_duration: Duration) -> bool {
        self.window_start.elapsed() > window_duration
    }

    fn reset(&mut self) {
        self.count = 0;
        self.window_start = Instant::now();
    }

    fn increment(&mut self) {
        self.count += 1;
    }
}

/// In-memory rate limiter (for production, use Redis)
#[derive(Debug)]
pub struct InMemoryRateLimiter {
    buckets: Arc<Mutex<HashMap<String, RateLimitBucket>>>,
    config: RateLimitConfig,
}

impl InMemoryRateLimiter {
    pub fn new(config: RateLimitConfig) -> Self {
        Self {
            buckets: Arc::new(Mutex::new(HashMap::new())),
            config,
        }
    }

    /// Check if a request should be rate limited
    pub fn check_rate_limit(&self, key: &str) -> Result<RateLimitInfo, RateLimitError> {
        let mut buckets = self.buckets.lock().unwrap();
        
        // Get or create bucket for this key
        let bucket = buckets.entry(key.to_string()).or_insert_with(RateLimitBucket::new);
        
        // Reset bucket if window has expired
        if bucket.is_expired(self.config.window_duration) {
            bucket.reset();
        }
        
        // Check if limit is exceeded
        if bucket.count >= self.config.max_requests {
            let reset_time = bucket.window_start + self.config.window_duration;
            let retry_after = reset_time.duration_since(Instant::now()).as_secs();
            
            return Err(RateLimitError {
                retry_after,
                limit: self.config.max_requests,
                remaining: 0,
                reset_time,
            });
        }
        
        // Increment counter
        bucket.increment();
        
        let remaining = self.config.max_requests - bucket.count;
        let reset_time = bucket.window_start + self.config.window_duration;
        
        Ok(RateLimitInfo {
            limit: self.config.max_requests,
            remaining,
            reset_time,
        })
    }

    /// Clean up expired buckets (should be called periodically)
    pub fn cleanup_expired(&self) {
        let mut buckets = self.buckets.lock().unwrap();
        buckets.retain(|_, bucket| !bucket.is_expired(self.config.window_duration * 2));
    }
}

#[derive(Debug)]
pub struct RateLimitInfo {
    pub limit: u32,
    pub remaining: u32,
    pub reset_time: Instant,
}

#[derive(Debug)]
pub struct RateLimitError {
    pub retry_after: u64,
    pub limit: u32,
    pub remaining: u32,
    pub reset_time: Instant,
}

/// Rate limiting middleware
pub async fn rate_limit_middleware(
    State(limiter): State<Arc<InMemoryRateLimiter>>,
    headers: HeaderMap,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Extract rate limit key
    let key = extract_rate_limit_key(&limiter.config, &headers, &request);
    
    // Check rate limit
    match limiter.check_rate_limit(&key) {
        Ok(info) => {
            debug!("Rate limit check passed for key: {}, remaining: {}", key, info.remaining);
            
            // Process request
            let mut response = next.run(request).await;
            
            // Add rate limit headers to response
            add_rate_limit_headers(response.headers_mut(), &info);
            
            Ok(response)
        }
        Err(error) => {
            warn!("Rate limit exceeded for key: {}, retry after: {}s", key, error.retry_after);
            
            // Create rate limit exceeded response
            let mut response = Response::new(
                format!(
                    "{{\"error\":\"Rate limit exceeded\",\"retry_after\":{},\"limit\":{}}}",
                    error.retry_after, error.limit
                ).into()
            );
            
            *response.status_mut() = StatusCode::TOO_MANY_REQUESTS;
            
            // Add rate limit headers
            let info = RateLimitInfo {
                limit: error.limit,
                remaining: error.remaining,
                reset_time: error.reset_time,
            };
            add_rate_limit_headers(response.headers_mut(), &info);
            
            // Add Retry-After header
            if let Ok(retry_after) = HeaderValue::from_str(&error.retry_after.to_string()) {
                response.headers_mut().insert("Retry-After", retry_after);
            }
            
            Ok(response)
        }
    }
}

/// Extract rate limit key based on configuration
fn extract_rate_limit_key(
    config: &RateLimitConfig,
    headers: &HeaderMap,
    request: &Request,
) -> String {
    let mut key_parts = Vec::new();
    
    if config.per_ip {
        let ip = extract_ip_address(headers);
        key_parts.push(format!("ip:{}", ip));
    }
    
    if config.per_user {
        // Try to extract user ID from request extensions (set by auth middleware)
        if let Some(user_id) = request.extensions().get::<uuid::Uuid>() {
            key_parts.push(format!("user:{}", user_id));
        }
    }
    
    // If no specific key parts, use IP as fallback
    if key_parts.is_empty() {
        let ip = extract_ip_address(headers);
        key_parts.push(format!("ip:{}", ip));
    }
    
    key_parts.join(":")
}

/// Extract IP address from headers (same as in audit middleware)
fn extract_ip_address(headers: &HeaderMap) -> String {
    let ip_headers = [
        "x-forwarded-for",
        "x-real-ip",
        "cf-connecting-ip",
        "x-client-ip",
        "x-forwarded",
        "forwarded-for",
        "forwarded",
    ];

    for header_name in &ip_headers {
        if let Some(header_value) = headers.get(header_name) {
            if let Ok(value) = header_value.to_str() {
                let ip = value.split(',').next().unwrap_or(value).trim();
                if !ip.is_empty() && ip != "unknown" {
                    return ip.to_string();
                }
            }
        }
    }

    "unknown".to_string()
}

/// Add rate limit headers to response
fn add_rate_limit_headers(headers: &mut HeaderMap, info: &RateLimitInfo) {
    if let Ok(limit) = HeaderValue::from_str(&info.limit.to_string()) {
        headers.insert("X-RateLimit-Limit", limit);
    }
    
    if let Ok(remaining) = HeaderValue::from_str(&info.remaining.to_string()) {
        headers.insert("X-RateLimit-Remaining", remaining);
    }
    
    let reset_timestamp = info.reset_time.duration_since(std::time::UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();
    
    if let Ok(reset) = HeaderValue::from_str(&reset_timestamp.to_string()) {
        headers.insert("X-RateLimit-Reset", reset);
    }
}

/// Different rate limit configurations for different endpoints
pub fn create_rate_limiters() -> HashMap<String, Arc<InMemoryRateLimiter>> {
    let mut limiters = HashMap::new();
    
    // General API rate limit
    limiters.insert(
        "general".to_string(),
        Arc::new(InMemoryRateLimiter::new(RateLimitConfig {
            max_requests: 100,
            window_duration: Duration::from_secs(60),
            per_ip: true,
            per_user: false,
        }))
    );
    
    // Authentication endpoints - stricter limits
    limiters.insert(
        "auth".to_string(),
        Arc::new(InMemoryRateLimiter::new(RateLimitConfig {
            max_requests: 10,
            window_duration: Duration::from_secs(60),
            per_ip: true,
            per_user: false,
        }))
    );
    
    // File upload endpoints - very strict limits
    limiters.insert(
        "upload".to_string(),
        Arc::new(InMemoryRateLimiter::new(RateLimitConfig {
            max_requests: 5,
            window_duration: Duration::from_secs(60),
            per_ip: true,
            per_user: true,
        }))
    );
    
    // Export endpoints - moderate limits
    limiters.insert(
        "export".to_string(),
        Arc::new(InMemoryRateLimiter::new(RateLimitConfig {
            max_requests: 20,
            window_duration: Duration::from_secs(300), // 5 minutes
            per_ip: true,
            per_user: true,
        }))
    );
    
    limiters
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;

    #[test]
    fn test_rate_limiter_basic() {
        let config = RateLimitConfig {
            max_requests: 3,
            window_duration: Duration::from_secs(60),
            per_ip: true,
            per_user: false,
        };
        
        let limiter = InMemoryRateLimiter::new(config);
        let key = "test_key";
        
        // First 3 requests should pass
        for i in 0..3 {
            let result = limiter.check_rate_limit(key);
            assert!(result.is_ok(), "Request {} should pass", i + 1);
            
            if let Ok(info) = result {
                assert_eq!(info.remaining, 3 - (i + 1) as u32);
            }
        }
        
        // 4th request should fail
        let result = limiter.check_rate_limit(key);
        assert!(result.is_err(), "4th request should be rate limited");
    }

    #[test]
    fn test_rate_limiter_window_reset() {
        let config = RateLimitConfig {
            max_requests: 2,
            window_duration: Duration::from_millis(100),
            per_ip: true,
            per_user: false,
        };
        
        let limiter = InMemoryRateLimiter::new(config);
        let key = "test_key";
        
        // Use up the limit
        assert!(limiter.check_rate_limit(key).is_ok());
        assert!(limiter.check_rate_limit(key).is_ok());
        assert!(limiter.check_rate_limit(key).is_err());
        
        // Wait for window to reset
        thread::sleep(Duration::from_millis(150));
        
        // Should be able to make requests again
        assert!(limiter.check_rate_limit(key).is_ok());
    }
}
