use std::sync::Arc;
use axum::{
    extract::{Request, State},
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::Response,
};
use tracing::{info, warn, error};
use uuid::Uuid;

use crate::domain::services::{AuditService, RequestContext};
use crate::infrastructure::config::AppConfig;

#[derive(Clone)]
pub struct AuditMiddlewareState {
    pub audit_service: Arc<AuditService>,
    pub config: AppConfig,
}

/// Middleware to automatically log HTTP requests and responses
pub async fn audit_middleware(
    State(state): State<AuditMiddlewareState>,
    headers: HeaderMap,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let start_time = std::time::Instant::now();
    
    // Generate request ID if not present
    let request_id = headers
        .get("x-request-id")
        .and_then(|v| v.to_str().ok())
        .unwrap_or(&Uuid::new_v4().to_string())
        .to_string();

    // Extract request information
    let method = request.method().to_string();
    let uri = request.uri().to_string();
    let ip_address = extract_ip_address(&headers);
    let user_agent = headers
        .get("user-agent")
        .and_then(|v| v.to_str().ok())
        .unwrap_or("unknown")
        .to_string();

    // Extract session ID from headers or cookies
    let session_id = extract_session_id(&headers);

    // Extract user ID from request extensions (set by auth middleware)
    let user_id = request.extensions().get::<Uuid>().copied();
    let tenant_id = request.extensions().get::<Uuid>().copied();
    let impersonated_by = request.extensions().get::<Option<Uuid>>().and_then(|&id| id);

    // Create request context
    let mut request_context = RequestContext::new(
        request_id.clone(),
        session_id,
        ip_address,
        user_agent,
        uri.clone(),
        method.clone(),
    );

    if let Some(impersonator) = impersonated_by {
        request_context = request_context.with_impersonation(impersonator);
    }

    // Add request context to request extensions for use in handlers
    request.extensions_mut().insert(request_context.clone());

    info!(
        request_id = %request_id,
        method = %method,
        uri = %uri,
        user_id = ?user_id,
        tenant_id = ?tenant_id,
        "Processing request"
    );

    // Process the request
    let response = next.run(request).await;
    let status_code = response.status().as_u16() as i32;
    let duration = start_time.elapsed();

    // Update request context with response status
    let request_context = request_context.with_status_code(status_code);

    // Log the request completion
    if status_code >= 400 {
        warn!(
            request_id = %request_id,
            method = %method,
            uri = %uri,
            status_code = %status_code,
            duration_ms = %duration.as_millis(),
            user_id = ?user_id,
            "Request completed with error"
        );
    } else {
        info!(
            request_id = %request_id,
            method = %method,
            uri = %uri,
            status_code = %status_code,
            duration_ms = %duration.as_millis(),
            user_id = ?user_id,
            "Request completed successfully"
        );
    }

    // Log HTTP request to audit system (async, don't block response)
    let audit_service = state.audit_service.clone();
    let log_context = request_context.clone();
    tokio::spawn(async move {
        if let Err(e) = audit_service.log_simple_action(
            tenant_id,
            user_id,
            "http_request",
            "system",
            Uuid::new_v4(), // Generate a UUID for the request entity
            Some(log_context),
        ).await {
            error!("Failed to log HTTP request to audit system: {}", e);
        }
    });

    // Warn about slow requests
    if duration.as_millis() > 1000 {
        warn!(
            request_id = %request_id,
            duration_ms = %duration.as_millis(),
            "Slow request detected"
        );
    }

    Ok(response)
}

/// Extract IP address from headers, considering proxy headers
fn extract_ip_address(headers: &HeaderMap) -> String {
    // Check for common proxy headers in order of preference
    let ip_headers = [
        "x-forwarded-for",
        "x-real-ip",
        "cf-connecting-ip", // Cloudflare
        "x-client-ip",
        "x-forwarded",
        "forwarded-for",
        "forwarded",
    ];

    for header_name in &ip_headers {
        if let Some(header_value) = headers.get(header_name) {
            if let Ok(value) = header_value.to_str() {
                // X-Forwarded-For can contain multiple IPs, take the first one
                let ip = value.split(',').next().unwrap_or(value).trim();
                if !ip.is_empty() && ip != "unknown" {
                    return ip.to_string();
                }
            }
        }
    }

    "unknown".to_string()
}

/// Extract session ID from headers or cookies
fn extract_session_id(headers: &HeaderMap) -> String {
    // Try to get session ID from Authorization header (JWT)
    if let Some(auth_header) = headers.get("authorization") {
        if let Ok(auth_value) = auth_header.to_str() {
            if auth_value.starts_with("Bearer ") {
                // In a real implementation, you would decode the JWT to get the session ID
                // For now, we'll use a hash of the token as session ID
                return format!("jwt_{}", calculate_hash(auth_value));
            }
        }
    }

    // Try to get session ID from custom header
    if let Some(session_header) = headers.get("x-session-id") {
        if let Ok(session_value) = session_header.to_str() {
            return session_value.to_string();
        }
    }

    // Try to get session ID from cookies
    if let Some(cookie_header) = headers.get("cookie") {
        if let Ok(cookie_value) = cookie_header.to_str() {
            for cookie in cookie_value.split(';') {
                let cookie = cookie.trim();
                if cookie.starts_with("session_id=") {
                    return cookie.replace("session_id=", "");
                }
            }
        }
    }

    // Generate a temporary session ID
    format!("temp_{}", Uuid::new_v4())
}

/// Simple hash function for generating session IDs from tokens
fn calculate_hash(input: &str) -> String {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};

    let mut hasher = DefaultHasher::new();
    input.hash(&mut hasher);
    format!("{:x}", hasher.finish())
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::HeaderValue;

    #[test]
    fn test_extract_ip_address() {
        let mut headers = HeaderMap::new();
        
        // Test X-Forwarded-For header
        headers.insert("x-forwarded-for", HeaderValue::from_static("***********, ********"));
        assert_eq!(extract_ip_address(&headers), "***********");
        
        // Test X-Real-IP header
        headers.clear();
        headers.insert("x-real-ip", HeaderValue::from_static("***********"));
        assert_eq!(extract_ip_address(&headers), "***********");
        
        // Test no headers
        headers.clear();
        assert_eq!(extract_ip_address(&headers), "unknown");
    }

    #[test]
    fn test_extract_session_id() {
        let mut headers = HeaderMap::new();
        
        // Test Authorization header
        headers.insert("authorization", HeaderValue::from_static("Bearer token123"));
        let session_id = extract_session_id(&headers);
        assert!(session_id.starts_with("jwt_"));
        
        // Test X-Session-ID header
        headers.clear();
        headers.insert("x-session-id", HeaderValue::from_static("session123"));
        assert_eq!(extract_session_id(&headers), "session123");
        
        // Test cookie
        headers.clear();
        headers.insert("cookie", HeaderValue::from_static("session_id=cookie123; other=value"));
        assert_eq!(extract_session_id(&headers), "cookie123");
        
        // Test no session info
        headers.clear();
        let session_id = extract_session_id(&headers);
        assert!(session_id.starts_with("temp_"));
    }
}
