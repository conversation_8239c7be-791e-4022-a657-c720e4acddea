use std::sync::{<PERSON>, Mutex};
use std::time::{Duration, Instant};
use std::collections::HashMap;
use tokio::time::sleep;
use tracing::{info, warn, error};

use crate::error::{AppError, AppResult};

/// Error recovery service with retry logic and circuit breaker
pub struct ErrorRecoveryService {
    circuit_breakers: Arc<Mutex<HashMap<String, CircuitBreaker>>>,
    retry_config: RetryConfig,
}

#[derive(Debug, Clone)]
pub struct RetryConfig {
    pub max_attempts: u32,
    pub base_delay_ms: u64,
    pub max_delay_ms: u64,
    pub backoff_multiplier: f64,
    pub jitter: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            base_delay_ms: 100,
            max_delay_ms: 30000, // 30 seconds
            backoff_multiplier: 2.0,
            jitter: true,
        }
    }
}

#[derive(Debug, <PERSON><PERSON>)]
struct CircuitBreaker {
    state: CircuitBreakerState,
    failure_count: u32,
    last_failure_time: Option<Instant>,
    success_count: u32,
    failure_threshold: u32,
    recovery_timeout: Duration,
    half_open_max_calls: u32,
}

#[derive(Debug, Clone, PartialEq)]
enum CircuitBreakerState {
    Closed,   // Normal operation
    Open,     // Failing, reject calls
    HalfOpen, // Testing if service recovered
}

impl CircuitBreaker {
    fn new(failure_threshold: u32, recovery_timeout: Duration) -> Self {
        Self {
            state: CircuitBreakerState::Closed,
            failure_count: 0,
            last_failure_time: None,
            success_count: 0,
            failure_threshold,
            recovery_timeout,
            half_open_max_calls: 3,
        }
    }

    fn can_execute(&mut self) -> bool {
        match self.state {
            CircuitBreakerState::Closed => true,
            CircuitBreakerState::Open => {
                if let Some(last_failure) = self.last_failure_time {
                    if last_failure.elapsed() > self.recovery_timeout {
                        self.state = CircuitBreakerState::HalfOpen;
                        self.success_count = 0;
                        info!("Circuit breaker transitioning to half-open state");
                        true
                    } else {
                        false
                    }
                } else {
                    false
                }
            }
            CircuitBreakerState::HalfOpen => self.success_count < self.half_open_max_calls,
        }
    }

    fn record_success(&mut self) {
        match self.state {
            CircuitBreakerState::Closed => {
                self.failure_count = 0;
            }
            CircuitBreakerState::HalfOpen => {
                self.success_count += 1;
                if self.success_count >= self.half_open_max_calls {
                    self.state = CircuitBreakerState::Closed;
                    self.failure_count = 0;
                    info!("Circuit breaker recovered, transitioning to closed state");
                }
            }
            CircuitBreakerState::Open => {
                // Should not happen
            }
        }
    }

    fn record_failure(&mut self) {
        self.failure_count += 1;
        self.last_failure_time = Some(Instant::now());

        match self.state {
            CircuitBreakerState::Closed => {
                if self.failure_count >= self.failure_threshold {
                    self.state = CircuitBreakerState::Open;
                    warn!("Circuit breaker opened due to {} failures", self.failure_count);
                }
            }
            CircuitBreakerState::HalfOpen => {
                self.state = CircuitBreakerState::Open;
                warn!("Circuit breaker reopened during half-open state");
            }
            CircuitBreakerState::Open => {
                // Already open
            }
        }
    }
}

impl ErrorRecoveryService {
    pub fn new(retry_config: RetryConfig) -> Self {
        Self {
            circuit_breakers: Arc::new(Mutex::new(HashMap::new())),
            retry_config,
        }
    }

    /// Execute an operation with retry logic and circuit breaker protection
    pub async fn execute_with_recovery<F, T, E>(
        &self,
        operation_name: &str,
        operation: F,
    ) -> AppResult<T>
    where
        F: Fn() -> Result<T, E> + Send + Sync,
        E: Into<AppError> + std::fmt::Debug,
    {
        // Check circuit breaker
        if !self.can_execute(operation_name) {
            return Err(AppError::service_unavailable(format!(
                "Circuit breaker is open for operation: {}",
                operation_name
            )));
        }

        let mut last_error = None;
        
        for attempt in 1..=self.retry_config.max_attempts {
            match operation() {
                Ok(result) => {
                    if attempt > 1 {
                        info!("Operation '{}' succeeded on attempt {}", operation_name, attempt);
                    }
                    self.record_success(operation_name);
                    return Ok(result);
                }
                Err(e) => {
                    let app_error: AppError = e.into();
                    last_error = Some(app_error.clone());

                    if !app_error.is_retryable() {
                        warn!("Operation '{}' failed with non-retryable error: {}", operation_name, app_error);
                        self.record_failure(operation_name);
                        return Err(app_error);
                    }

                    if attempt < self.retry_config.max_attempts {
                        let delay = self.calculate_delay(attempt);
                        warn!(
                            "Operation '{}' failed on attempt {} ({}), retrying in {}ms",
                            operation_name,
                            attempt,
                            app_error,
                            delay.as_millis()
                        );
                        sleep(delay).await;
                    } else {
                        error!("Operation '{}' failed after {} attempts: {}", operation_name, attempt, app_error);
                        self.record_failure(operation_name);
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| AppError::internal("Unknown error during retry")))
    }

    /// Execute an async operation with retry logic
    pub async fn execute_async_with_recovery<F, Fut, T, E>(
        &self,
        operation_name: &str,
        operation: F,
    ) -> AppResult<T>
    where
        F: Fn() -> Fut + Send + Sync,
        Fut: std::future::Future<Output = Result<T, E>> + Send,
        E: Into<AppError> + std::fmt::Debug,
        T: Send,
    {
        // Check circuit breaker
        if !self.can_execute(operation_name) {
            return Err(AppError::service_unavailable(format!(
                "Circuit breaker is open for operation: {}",
                operation_name
            )));
        }

        let mut last_error = None;
        
        for attempt in 1..=self.retry_config.max_attempts {
            match operation().await {
                Ok(result) => {
                    if attempt > 1 {
                        info!("Async operation '{}' succeeded on attempt {}", operation_name, attempt);
                    }
                    self.record_success(operation_name);
                    return Ok(result);
                }
                Err(e) => {
                    let app_error: AppError = e.into();
                    last_error = Some(app_error.clone());

                    if !app_error.is_retryable() {
                        warn!("Async operation '{}' failed with non-retryable error: {}", operation_name, app_error);
                        self.record_failure(operation_name);
                        return Err(app_error);
                    }

                    if attempt < self.retry_config.max_attempts {
                        let delay = self.calculate_delay(attempt);
                        warn!(
                            "Async operation '{}' failed on attempt {} ({}), retrying in {}ms",
                            operation_name,
                            attempt,
                            app_error,
                            delay.as_millis()
                        );
                        sleep(delay).await;
                    } else {
                        error!("Async operation '{}' failed after {} attempts: {}", operation_name, attempt, app_error);
                        self.record_failure(operation_name);
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| AppError::internal("Unknown error during async retry")))
    }

    /// Calculate delay for exponential backoff with jitter
    fn calculate_delay(&self, attempt: u32) -> Duration {
        let base_delay = self.retry_config.base_delay_ms as f64;
        let multiplier = self.retry_config.backoff_multiplier;
        
        let delay_ms = base_delay * multiplier.powi((attempt - 1) as i32);
        let delay_ms = delay_ms.min(self.retry_config.max_delay_ms as f64);
        
        let delay_ms = if self.retry_config.jitter {
            // Add random jitter (±25%)
            let jitter_range = delay_ms * 0.25;
            let jitter = (rand::random::<f64>() - 0.5) * 2.0 * jitter_range;
            (delay_ms + jitter).max(0.0)
        } else {
            delay_ms
        };

        Duration::from_millis(delay_ms as u64)
    }

    /// Check if operation can be executed (circuit breaker check)
    fn can_execute(&self, operation_name: &str) -> bool {
        let mut breakers = self.circuit_breakers.lock().unwrap();
        let breaker = breakers
            .entry(operation_name.to_string())
            .or_insert_with(|| CircuitBreaker::new(5, Duration::from_secs(60)));
        
        breaker.can_execute()
    }

    /// Record successful operation
    fn record_success(&self, operation_name: &str) {
        let mut breakers = self.circuit_breakers.lock().unwrap();
        if let Some(breaker) = breakers.get_mut(operation_name) {
            breaker.record_success();
        }
    }

    /// Record failed operation
    fn record_failure(&self, operation_name: &str) {
        let mut breakers = self.circuit_breakers.lock().unwrap();
        let breaker = breakers
            .entry(operation_name.to_string())
            .or_insert_with(|| CircuitBreaker::new(5, Duration::from_secs(60)));
        
        breaker.record_failure();
    }

    /// Get circuit breaker status for monitoring
    pub fn get_circuit_breaker_status(&self) -> HashMap<String, CircuitBreakerStatus> {
        let breakers = self.circuit_breakers.lock().unwrap();
        breakers
            .iter()
            .map(|(name, breaker)| {
                (
                    name.clone(),
                    CircuitBreakerStatus {
                        state: format!("{:?}", breaker.state),
                        failure_count: breaker.failure_count,
                        success_count: breaker.success_count,
                        last_failure_time: breaker.last_failure_time,
                    },
                )
            })
            .collect()
    }
}

#[derive(Debug, Clone, serde::Serialize)]
pub struct CircuitBreakerStatus {
    pub state: String,
    pub failure_count: u32,
    pub success_count: u32,
    pub last_failure_time: Option<Instant>,
}

/// Macro for easy retry execution
#[macro_export]
macro_rules! retry_operation {
    ($recovery_service:expr, $operation_name:expr, $operation:expr) => {
        $recovery_service
            .execute_with_recovery($operation_name, || $operation)
            .await
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::{AtomicU32, Ordering};

    #[tokio::test]
    async fn test_retry_success_on_second_attempt() {
        let recovery_service = ErrorRecoveryService::new(RetryConfig::default());
        let attempt_count = Arc::new(AtomicU32::new(0));
        let attempt_count_clone = attempt_count.clone();

        let result = recovery_service
            .execute_with_recovery("test_operation", || {
                let count = attempt_count_clone.fetch_add(1, Ordering::SeqCst);
                if count == 0 {
                    Err(AppError::timeout("First attempt fails"))
                } else {
                    Ok("success")
                }
            })
            .await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "success");
        assert_eq!(attempt_count.load(Ordering::SeqCst), 2);
    }

    #[tokio::test]
    async fn test_non_retryable_error() {
        let recovery_service = ErrorRecoveryService::new(RetryConfig::default());
        let attempt_count = Arc::new(AtomicU32::new(0));
        let attempt_count_clone = attempt_count.clone();

        let result = recovery_service
            .execute_with_recovery("test_operation", || {
                attempt_count_clone.fetch_add(1, Ordering::SeqCst);
                Err(AppError::validation("Non-retryable error"))
            })
            .await;

        assert!(result.is_err());
        assert_eq!(attempt_count.load(Ordering::SeqCst), 1); // Should not retry
    }
}
