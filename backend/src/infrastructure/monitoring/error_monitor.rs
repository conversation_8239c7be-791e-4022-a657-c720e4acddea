use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::time::interval;
use tracing::{info, warn, error};

use crate::error::AppError;

/// Error monitoring service for tracking and alerting on application errors
pub struct ErrorMonitoringService {
    error_stats: Arc<Mutex<ErrorStatistics>>,
    alert_thresholds: AlertThresholds,
    alert_handlers: Vec<Box<dyn AlertHandler>>,
}

#[derive(Debug, <PERSON>lone)]
struct ErrorStatistics {
    error_counts: HashMap<String, ErrorCount>,
    total_errors: u64,
    last_reset: Instant,
    error_rate_window: Duration,
}

#[derive(Debug, <PERSON>lone)]
struct ErrorCount {
    count: u64,
    first_occurrence: Instant,
    last_occurrence: Instant,
    error_rate: f64, // errors per minute
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct AlertThresholds {
    pub error_rate_threshold: f64,        // errors per minute
    pub critical_error_threshold: u32,    // immediate alert threshold
    pub error_spike_multiplier: f64,      // alert if error rate increases by this factor
    pub monitoring_window: Duration,       // time window for rate calculations
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            error_rate_threshold: 10.0, // 10 errors per minute
            critical_error_threshold: 5, // 5 critical errors triggers immediate alert
            error_spike_multiplier: 3.0, // 3x increase triggers alert
            monitoring_window: Duration::from_secs(300), // 5 minutes
        }
    }
}

/// Trait for handling alerts
pub trait AlertHandler: Send + Sync {
    fn handle_alert(&self, alert: &ErrorAlert);
}

#[derive(Debug, Clone)]
pub struct ErrorAlert {
    pub alert_type: AlertType,
    pub error_code: String,
    pub message: String,
    pub error_count: u64,
    pub error_rate: f64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub severity: AlertSeverity,
    pub context: HashMap<String, String>,
}

#[derive(Debug, Clone)]
pub enum AlertType {
    HighErrorRate,
    ErrorSpike,
    CriticalError,
    ServiceDown,
    CircuitBreakerOpen,
}

#[derive(Debug, Clone)]
pub enum AlertSeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl ErrorMonitoringService {
    pub fn new(alert_thresholds: AlertThresholds) -> Self {
        Self {
            error_stats: Arc::new(Mutex::new(ErrorStatistics {
                error_counts: HashMap::new(),
                total_errors: 0,
                last_reset: Instant::now(),
                error_rate_window: alert_thresholds.monitoring_window,
            })),
            alert_thresholds,
            alert_handlers: Vec::new(),
        }
    }

    /// Add an alert handler
    pub fn add_alert_handler(&mut self, handler: Box<dyn AlertHandler>) {
        self.alert_handlers.push(handler);
    }

    /// Record an error occurrence
    pub fn record_error(&self, error: &AppError, context: Option<HashMap<String, String>>) {
        let error_code = error.error_code().to_string();
        let now = Instant::now();
        
        let mut stats = self.error_stats.lock().unwrap();
        stats.total_errors += 1;

        // Update error count for this specific error type
        let error_count = stats.error_counts
            .entry(error_code.clone())
            .or_insert_with(|| ErrorCount {
                count: 0,
                first_occurrence: now,
                last_occurrence: now,
                error_rate: 0.0,
            });

        error_count.count += 1;
        error_count.last_occurrence = now;

        // Calculate error rate (errors per minute)
        let time_window = now.duration_since(error_count.first_occurrence);
        if time_window.as_secs() > 0 {
            error_count.error_rate = (error_count.count as f64) / (time_window.as_secs() as f64 / 60.0);
        }

        // Check for immediate alerts
        self.check_for_alerts(&error_code, error_count, error, context.unwrap_or_default());
    }

    /// Check if alerts should be triggered
    fn check_for_alerts(
        &self,
        error_code: &str,
        error_count: &ErrorCount,
        error: &AppError,
        context: HashMap<String, String>,
    ) {
        // Check for critical errors that need immediate attention
        if self.is_critical_error(error) {
            let alert = ErrorAlert {
                alert_type: AlertType::CriticalError,
                error_code: error_code.to_string(),
                message: format!("Critical error occurred: {}", error),
                error_count: error_count.count,
                error_rate: error_count.error_rate,
                timestamp: chrono::Utc::now(),
                severity: AlertSeverity::Critical,
                context,
            };
            self.send_alert(alert);
        }

        // Check for high error rate
        if error_count.error_rate > self.alert_thresholds.error_rate_threshold {
            let alert = ErrorAlert {
                alert_type: AlertType::HighErrorRate,
                error_code: error_code.to_string(),
                message: format!(
                    "High error rate detected: {:.2} errors/min for {}",
                    error_count.error_rate, error_code
                ),
                error_count: error_count.count,
                error_rate: error_count.error_rate,
                timestamp: chrono::Utc::now(),
                severity: AlertSeverity::High,
                context,
            };
            self.send_alert(alert);
        }

        // Check for error spikes (TODO: implement baseline comparison)
        // This would require storing historical data to compare against
    }

    /// Check if an error is considered critical
    fn is_critical_error(&self, error: &AppError) -> bool {
        matches!(
            error,
            AppError::Database(_)
                | AppError::ServiceUnavailable { .. }
                | AppError::Configuration { .. }
                | AppError::Internal { .. }
        )
    }

    /// Send alert to all registered handlers
    fn send_alert(&self, alert: ErrorAlert) {
        for handler in &self.alert_handlers {
            handler.handle_alert(&alert);
        }
    }

    /// Get current error statistics
    pub fn get_error_statistics(&self) -> ErrorStatisticsReport {
        let stats = self.error_stats.lock().unwrap();
        let now = Instant::now();
        let uptime = now.duration_since(stats.last_reset);

        let error_details: Vec<ErrorDetail> = stats.error_counts
            .iter()
            .map(|(code, count)| ErrorDetail {
                error_code: code.clone(),
                count: count.count,
                error_rate: count.error_rate,
                first_occurrence: count.first_occurrence,
                last_occurrence: count.last_occurrence,
            })
            .collect();

        ErrorStatisticsReport {
            total_errors: stats.total_errors,
            uptime_seconds: uptime.as_secs(),
            overall_error_rate: if uptime.as_secs() > 0 {
                (stats.total_errors as f64) / (uptime.as_secs() as f64 / 60.0)
            } else {
                0.0
            },
            error_details,
            monitoring_window_seconds: stats.error_rate_window.as_secs(),
        }
    }

    /// Reset error statistics
    pub fn reset_statistics(&self) {
        let mut stats = self.error_stats.lock().unwrap();
        stats.error_counts.clear();
        stats.total_errors = 0;
        stats.last_reset = Instant::now();
        info!("Error statistics reset");
    }

    /// Start background monitoring task
    pub fn start_monitoring(self: Arc<Self>) {
        let service = self.clone();
        tokio::spawn(async move {
            let mut interval_timer = interval(Duration::from_secs(60)); // Check every minute
            
            loop {
                interval_timer.tick().await;
                service.perform_periodic_checks().await;
            }
        });
        
        info!("Error monitoring service started");
    }

    /// Perform periodic health checks and cleanup
    async fn perform_periodic_checks(&self) {
        // Clean up old error data
        self.cleanup_old_data();
        
        // Check for service health issues
        self.check_service_health();
        
        // Log current statistics
        let stats = self.get_error_statistics();
        info!(
            "Error monitoring report - Total errors: {}, Error rate: {:.2}/min, Unique error types: {}",
            stats.total_errors,
            stats.overall_error_rate,
            stats.error_details.len()
        );
    }

    /// Clean up old error data outside the monitoring window
    fn cleanup_old_data(&self) {
        let mut stats = self.error_stats.lock().unwrap();
        let now = Instant::now();
        let cutoff = now - self.alert_thresholds.monitoring_window;

        stats.error_counts.retain(|_, count| {
            count.last_occurrence > cutoff
        });
    }

    /// Check overall service health
    fn check_service_health(&self) {
        let stats = self.error_stats.lock().unwrap();
        let now = Instant::now();
        let uptime = now.duration_since(stats.last_reset);
        
        if uptime.as_secs() > 0 {
            let overall_error_rate = (stats.total_errors as f64) / (uptime.as_secs() as f64 / 60.0);
            
            if overall_error_rate > self.alert_thresholds.error_rate_threshold * 2.0 {
                let alert = ErrorAlert {
                    alert_type: AlertType::ServiceDown,
                    error_code: "SERVICE_HEALTH".to_string(),
                    message: format!("Service health degraded - overall error rate: {:.2}/min", overall_error_rate),
                    error_count: stats.total_errors,
                    error_rate: overall_error_rate,
                    timestamp: chrono::Utc::now(),
                    severity: AlertSeverity::Critical,
                    context: HashMap::new(),
                };
                
                drop(stats); // Release lock before sending alert
                self.send_alert(alert);
            }
        }
    }
}

#[derive(Debug, Clone, serde::Serialize)]
pub struct ErrorStatisticsReport {
    pub total_errors: u64,
    pub uptime_seconds: u64,
    pub overall_error_rate: f64,
    pub error_details: Vec<ErrorDetail>,
    pub monitoring_window_seconds: u64,
}

#[derive(Debug, Clone, serde::Serialize)]
pub struct ErrorDetail {
    pub error_code: String,
    pub count: u64,
    pub error_rate: f64,
    pub first_occurrence: Instant,
    pub last_occurrence: Instant,
}

/// Console alert handler for development
pub struct ConsoleAlertHandler;

impl AlertHandler for ConsoleAlertHandler {
    fn handle_alert(&self, alert: &ErrorAlert) {
        match alert.severity {
            AlertSeverity::Critical => {
                error!("🚨 CRITICAL ALERT: {} - {}", alert.error_code, alert.message);
            }
            AlertSeverity::High => {
                warn!("⚠️  HIGH ALERT: {} - {}", alert.error_code, alert.message);
            }
            AlertSeverity::Medium => {
                warn!("⚠️  MEDIUM ALERT: {} - {}", alert.error_code, alert.message);
            }
            AlertSeverity::Low => {
                info!("ℹ️  LOW ALERT: {} - {}", alert.error_code, alert.message);
            }
        }
    }
}

/// Email alert handler (placeholder implementation)
pub struct EmailAlertHandler {
    recipients: Vec<String>,
}

impl EmailAlertHandler {
    pub fn new(recipients: Vec<String>) -> Self {
        Self { recipients }
    }
}

impl AlertHandler for EmailAlertHandler {
    fn handle_alert(&self, alert: &ErrorAlert) {
        // In a real implementation, this would send emails
        info!(
            "Would send email alert to {:?}: {} - {}",
            self.recipients, alert.error_code, alert.message
        );
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_recording() {
        let mut monitor = ErrorMonitoringService::new(AlertThresholds::default());
        monitor.add_alert_handler(Box::new(ConsoleAlertHandler));

        let error = AppError::validation("Test error");
        monitor.record_error(&error, None);

        let stats = monitor.get_error_statistics();
        assert_eq!(stats.total_errors, 1);
        assert_eq!(stats.error_details.len(), 1);
        assert_eq!(stats.error_details[0].error_code, "VALIDATION_ERROR");
    }

    #[test]
    fn test_critical_error_detection() {
        let monitor = ErrorMonitoringService::new(AlertThresholds::default());
        
        let critical_error = AppError::internal("Critical system error");
        assert!(monitor.is_critical_error(&critical_error));
        
        let non_critical_error = AppError::validation("Validation error");
        assert!(!monitor.is_critical_error(&non_critical_error));
    }
}
