use std::sync::Arc;
use std::time::{Duration, Instant};
use serde::{Deserialize, Serialize};
use tokio::time::timeout;
use tracing::{info, warn, error};

use crate::infrastructure::metrics::MetricsService;

/// Health check service for monitoring system components
pub struct HealthCheckService {
    metrics: Arc<MetricsService>,
    checks: Vec<Box<dyn HealthCheck>>,
}

/// Health check status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum HealthStatus {
    Healthy,
    Degraded,
    Unhealthy,
}

/// Individual health check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckResult {
    pub name: String,
    pub status: HealthStatus,
    pub message: String,
    pub duration_ms: u64,
    pub details: Option<serde_json::Value>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Overall system health report
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct HealthReport {
    pub status: HealthStatus,
    pub checks: Vec<HealthCheckResult>,
    pub summary: HealthSummary,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Health summary statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthSummary {
    pub total_checks: usize,
    pub healthy_checks: usize,
    pub degraded_checks: usize,
    pub unhealthy_checks: usize,
    pub total_duration_ms: u64,
}

/// Trait for implementing health checks
pub trait HealthCheck: Send + Sync {
    fn name(&self) -> &str;
    async fn check(&self) -> HealthCheckResult;
}

impl HealthCheckService {
    pub fn new(metrics: Arc<MetricsService>) -> Self {
        let mut service = Self {
            metrics,
            checks: Vec::new(),
        };

        // Add default health checks
        service.add_check(Box::new(MemoryHealthCheck::new()));
        service.add_check(Box::new(DiskSpaceHealthCheck::new()));

        service
    }

    /// Add a custom health check
    pub fn add_check(&mut self, check: Box<dyn HealthCheck>) {
        self.checks.push(check);
    }

    /// Perform all health checks
    pub async fn check_health(&self) -> HealthReport {
        let start_time = Instant::now();
        let mut results = Vec::new();

        // Run all health checks concurrently
        let check_futures: Vec<_> = self.checks
            .iter()
            .map(|check| async move {
                let check_start = Instant::now();
                let result = timeout(Duration::from_secs(30), check.check()).await;
                
                match result {
                    Ok(mut check_result) => {
                        check_result.duration_ms = check_start.elapsed().as_millis() as u64;
                        check_result
                    }
                    Err(_) => HealthCheckResult {
                        name: check.name().to_string(),
                        status: HealthStatus::Unhealthy,
                        message: "Health check timed out".to_string(),
                        duration_ms: 30000,
                        details: None,
                        timestamp: chrono::Utc::now(),
                    }
                }
            })
            .collect();

        // Wait for all checks to complete
        for future in check_futures {
            let result = future.await;
            
            // Record metrics
            self.metrics.record_timer(
                &format!("health_check_duration_{}", result.name),
                Duration::from_millis(result.duration_ms)
            ).await;
            
            self.metrics.set_gauge(
                &format!("health_check_status_{}", result.name),
                match result.status {
                    HealthStatus::Healthy => 1.0,
                    HealthStatus::Degraded => 0.5,
                    HealthStatus::Unhealthy => 0.0,
                }
            ).await;

            results.push(result);
        }

        // Calculate overall status and summary
        let total_duration_ms = start_time.elapsed().as_millis() as u64;
        let summary = self.calculate_summary(&results, total_duration_ms);
        let overall_status = self.determine_overall_status(&results);

        // Log health check results
        match overall_status {
            HealthStatus::Healthy => info!("Health check completed - all systems healthy"),
            HealthStatus::Degraded => warn!("Health check completed - some systems degraded"),
            HealthStatus::Unhealthy => error!("Health check completed - critical systems unhealthy"),
        }

        HealthReport {
            status: overall_status,
            checks: results,
            summary,
            timestamp: chrono::Utc::now(),
        }
    }

    /// Get a quick health status (lightweight check)
    pub async fn quick_health(&self) -> HealthStatus {
        // Perform only critical checks for quick response
        let db_check = DatabaseHealthCheck::new(self.database_pool.clone());
        let result = timeout(Duration::from_secs(5), db_check.check()).await;

        match result {
            Ok(check_result) => check_result.status,
            Err(_) => HealthStatus::Unhealthy,
        }
    }

    fn calculate_summary(&self, results: &[HealthCheckResult], total_duration_ms: u64) -> HealthSummary {
        let total_checks = results.len();
        let healthy_checks = results.iter().filter(|r| r.status == HealthStatus::Healthy).count();
        let degraded_checks = results.iter().filter(|r| r.status == HealthStatus::Degraded).count();
        let unhealthy_checks = results.iter().filter(|r| r.status == HealthStatus::Unhealthy).count();

        HealthSummary {
            total_checks,
            healthy_checks,
            degraded_checks,
            unhealthy_checks,
            total_duration_ms,
        }
    }

    fn determine_overall_status(&self, results: &[HealthCheckResult]) -> HealthStatus {
        let has_unhealthy = results.iter().any(|r| r.status == HealthStatus::Unhealthy);
        let has_degraded = results.iter().any(|r| r.status == HealthStatus::Degraded);

        if has_unhealthy {
            HealthStatus::Unhealthy
        } else if has_degraded {
            HealthStatus::Degraded
        } else {
            HealthStatus::Healthy
        }
    }
}

/// Simple application health check
pub struct ApplicationHealthCheck;

impl ApplicationHealthCheck {
    pub fn new() -> Self {
        Self
    }
}

impl HealthCheck for ApplicationHealthCheck {
    fn name(&self) -> &str {
        "application"
    }

    async fn check(&self) -> HealthCheckResult {
        // Simple application health check
        HealthCheckResult {
            name: self.name().to_string(),
            status: HealthStatus::Healthy,
            message: "Application is running".to_string(),
            duration_ms: 1,
            details: Some(serde_json::json!({
                "uptime_seconds": std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs()
            })),
            timestamp: chrono::Utc::now(),
        }
    }
}

/// Memory health check
pub struct MemoryHealthCheck;

impl MemoryHealthCheck {
    pub fn new() -> Self {
        Self
    }
}

impl HealthCheck for MemoryHealthCheck {
    fn name(&self) -> &str {
        "memory"
    }

    async fn check(&self) -> HealthCheckResult {
        // This is a simplified memory check
        // In a real implementation, you'd use system APIs to get actual memory usage
        let memory_usage_percent = 45.0; // Placeholder value
        
        let (status, message) = if memory_usage_percent > 90.0 {
            (HealthStatus::Unhealthy, "Memory usage critical")
        } else if memory_usage_percent > 80.0 {
            (HealthStatus::Degraded, "Memory usage high")
        } else {
            (HealthStatus::Healthy, "Memory usage normal")
        };

        let details = serde_json::json!({
            "memory_usage_percent": memory_usage_percent,
            "threshold_warning": 80.0,
            "threshold_critical": 90.0
        });

        HealthCheckResult {
            name: self.name().to_string(),
            status,
            message: message.to_string(),
            duration_ms: 1, // Memory check is very fast
            details: Some(details),
            timestamp: chrono::Utc::now(),
        }
    }
}

/// Disk space health check
pub struct DiskSpaceHealthCheck;

impl DiskSpaceHealthCheck {
    pub fn new() -> Self {
        Self
    }
}

impl HealthCheck for DiskSpaceHealthCheck {
    fn name(&self) -> &str {
        "disk_space"
    }

    async fn check(&self) -> HealthCheckResult {
        // This is a simplified disk space check
        // In a real implementation, you'd check actual disk usage
        let disk_usage_percent = 65.0; // Placeholder value
        
        let (status, message) = if disk_usage_percent > 95.0 {
            (HealthStatus::Unhealthy, "Disk space critical")
        } else if disk_usage_percent > 85.0 {
            (HealthStatus::Degraded, "Disk space low")
        } else {
            (HealthStatus::Healthy, "Disk space sufficient")
        };

        let details = serde_json::json!({
            "disk_usage_percent": disk_usage_percent,
            "threshold_warning": 85.0,
            "threshold_critical": 95.0
        });

        HealthCheckResult {
            name: self.name().to_string(),
            status,
            message: message.to_string(),
            duration_ms: 1, // Disk check is very fast
            details: Some(details),
            timestamp: chrono::Utc::now(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_health_status_determination() {
        let results = vec![
            HealthCheckResult {
                name: "test1".to_string(),
                status: HealthStatus::Healthy,
                message: "OK".to_string(),
                duration_ms: 10,
                details: None,
                timestamp: chrono::Utc::now(),
            },
            HealthCheckResult {
                name: "test2".to_string(),
                status: HealthStatus::Degraded,
                message: "Warning".to_string(),
                duration_ms: 20,
                details: None,
                timestamp: chrono::Utc::now(),
            },
        ];

        // This would require creating a HealthCheckService instance
        // For now, just test the logic manually
        let has_unhealthy = results.iter().any(|r| r.status == HealthStatus::Unhealthy);
        let has_degraded = results.iter().any(|r| r.status == HealthStatus::Degraded);

        let overall_status = if has_unhealthy {
            HealthStatus::Unhealthy
        } else if has_degraded {
            HealthStatus::Degraded
        } else {
            HealthStatus::Healthy
        };

        assert_eq!(overall_status, HealthStatus::Degraded);
    }
}
