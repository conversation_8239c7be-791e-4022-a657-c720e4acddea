use std::sync::Arc;
use axum::{
    extract::{Multipart, Path, Query, State},
    http::StatusCode,
    response::{<PERSON>son, Response},
    Extension,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::domain::entities::{Attachment, CreateAttachmentRequest};
use crate::domain::services::FileService;
use crate::infrastructure::security::{SecurityIssue, SecuritySeverity};
use crate::error::{AppError, AppResult};
use crate::presentation::handlers::audit_handler::CurrentUser;

/// Application state for file handlers
#[derive(Clone)]
pub struct FileHandlerState {
    pub file_service: Arc<FileService>,
}

/// Query parameters for file listing
#[derive(Debug, Deserialize)]
pub struct FileListQuery {
    pub page: Option<u64>,
    pub limit: Option<u64>,
    pub category: Option<String>,
}

/// File upload response
#[derive(Debug, Serialize)]
pub struct FileUploadResponse {
    pub files: Vec<FileUploadResult>,
    pub success: bool,
    pub total_uploaded: u32,
    pub total_failed: u32,
}

/// Individual file upload result
#[derive(Debug, Serialize)]
pub struct FileUploadResult {
    pub success: bool,
    pub attachment: Option<AttachmentResponse>,
    pub filename: String,
    pub error: Option<String>,
    pub security_issues: Vec<SecurityIssueResponse>,
}

/// Attachment response
#[derive(Debug, Serialize)]
pub struct AttachmentResponse {
    pub id: Uuid,
    pub entity_type: String,
    pub entity_id: Uuid,
    pub file_name: String,
    pub original_name: String,
    pub file_size: i64,
    pub mime_type: String,
    pub category: Option<String>,
    pub description: Option<String>,
    pub is_public: bool,
    pub download_count: i32,
    pub created_date: chrono::DateTime<chrono::Utc>,
}

/// Security issue response
#[derive(Debug, Serialize)]
pub struct SecurityIssueResponse {
    pub severity: String,
    pub issue_type: String,
    pub description: String,
    pub recommendation: String,
}

/// File list response
#[derive(Debug, Serialize)]
pub struct FileListResponse {
    pub files: Vec<AttachmentResponse>,
    pub total: u64,
    pub page: u64,
    pub limit: u64,
}

/// Upload files for a specific entity
pub async fn upload_files(
    State(state): State<FileHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Path((entity_type, entity_id)): Path<(String, Uuid)>,
    mut multipart: Multipart,
) -> AppResult<Json<FileUploadResponse>> {
    let mut upload_results = Vec::new();
    let mut total_uploaded = 0u32;
    let mut total_failed = 0u32;

    // Process each file in the multipart upload
    while let Some(field) = multipart.next_field().await
        .map_err(|e| AppError::bad_request(format!("Failed to read multipart field: {}", e)))? 
    {
        let file_name = field.file_name()
            .ok_or_else(|| AppError::bad_request("No filename provided"))?
            .to_string();

        let content_type = field.content_type()
            .unwrap_or("application/octet-stream")
            .to_string();

        let data = field.bytes().await
            .map_err(|e| AppError::bad_request(format!("Failed to read file data: {}", e)))?;

        // Create attachment request
        let attachment_request = CreateAttachmentRequest {
            entity_type: entity_type.clone(),
            entity_id,
            original_name: file_name.clone(),
            file_size: data.len() as i64,
            mime_type: content_type,
            category: None,
            description: None,
            is_public: Some(false),
        };

        // Upload file using the file service
        match state.file_service.upload_file(
            current_user.tenant_id,
            attachment_request,
            data.to_vec(),
            Some(current_user.user_id),
        ).await {
            Ok(attachment) => {
                upload_results.push(FileUploadResult {
                    success: true,
                    attachment: Some(convert_attachment_to_response(attachment)),
                    filename: file_name,
                    error: None,
                    security_issues: vec![],
                });
                total_uploaded += 1;
            }
            Err(e) => {
                upload_results.push(FileUploadResult {
                    success: false,
                    attachment: None,
                    filename: file_name,
                    error: Some(e.to_string()),
                    security_issues: vec![], // TODO: Extract security issues from error
                });
                total_failed += 1;
            }
        }
    }

    Ok(Json(FileUploadResponse {
        files: upload_results,
        success: total_failed == 0,
        total_uploaded,
        total_failed,
    }))
}

/// Download a file by ID
pub async fn download_file(
    State(state): State<FileHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Path(file_id): Path<Uuid>,
) -> AppResult<Response> {
    let (attachment, file_content) = state.file_service
        .download_file(file_id, Some(current_user.user_id))
        .await
        .map_err(|e| AppError::not_found(format!("File not found: {}", e)))?;

    // Create response with appropriate headers
    let mut response = Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", &attachment.mime_type)
        .header("Content-Length", file_content.len().to_string())
        .header("Content-Disposition", format!("attachment; filename=\"{}\"", attachment.original_name))
        .header("X-File-ID", attachment.id.to_string())
        .body(file_content.into())
        .map_err(|e| AppError::internal(format!("Failed to create response: {}", e)))?;

    Ok(response)
}

/// List files for a specific entity
pub async fn list_files(
    State(state): State<FileHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Path((entity_type, entity_id)): Path<(String, Uuid)>,
    Query(query): Query<FileListQuery>,
) -> AppResult<Json<FileListResponse>> {
    let files = state.file_service
        .list_files_by_entity(&entity_type, entity_id)
        .await
        .map_err(|e| AppError::internal(format!("Failed to list files: {}", e)))?;

    // Apply filtering and pagination
    let filtered_files: Vec<_> = files
        .into_iter()
        .filter(|file| {
            // Filter by tenant
            file.tenant_id == current_user.tenant_id &&
            // Filter by category if specified
            query.category.as_ref().map_or(true, |cat| file.category.as_ref() == Some(cat))
        })
        .collect();

    let total = filtered_files.len() as u64;
    let page = query.page.unwrap_or(1);
    let limit = query.limit.unwrap_or(50).min(100);
    let offset = (page - 1) * limit;

    let paginated_files: Vec<_> = filtered_files
        .into_iter()
        .skip(offset as usize)
        .take(limit as usize)
        .map(convert_attachment_to_response)
        .collect();

    Ok(Json(FileListResponse {
        files: paginated_files,
        total,
        page,
        limit,
    }))
}

/// Delete a file by ID
pub async fn delete_file(
    State(state): State<FileHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Path(file_id): Path<Uuid>,
) -> AppResult<Json<serde_json::Value>> {
    state.file_service
        .delete_file(file_id, current_user.user_id)
        .await
        .map_err(|e| AppError::internal(format!("Failed to delete file: {}", e)))?;

    Ok(Json(serde_json::json!({
        "success": true,
        "message": "File deleted successfully"
    })))
}

/// Get file metadata by ID
pub async fn get_file_metadata(
    State(state): State<FileHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Path(file_id): Path<Uuid>,
) -> AppResult<Json<AttachmentResponse>> {
    // This would require adding a method to get attachment by ID
    // For now, return a not implemented error
    Err(AppError::internal("Get file metadata not yet implemented"))
}

/// Validate file before upload (dry run)
pub async fn validate_file(
    State(state): State<FileHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    mut multipart: Multipart,
) -> AppResult<Json<Vec<FileValidationResponse>>> {
    let mut validation_results = Vec::new();

    while let Some(field) = multipart.next_field().await
        .map_err(|e| AppError::bad_request(format!("Failed to read multipart field: {}", e)))?
    {
        let file_name = field.file_name()
            .ok_or_else(|| AppError::bad_request("No filename provided"))?
            .to_string();

        let content_type = field.content_type()
            .unwrap_or("application/octet-stream")
            .to_string();

        let data = field.bytes().await
            .map_err(|e| AppError::bad_request(format!("Failed to read file data: {}", e)))?;

        // Validate using the security service directly
        let validation_result = state.file_service.security_service
            .validate_file(&file_name, &data, Some(&content_type))
            .await?;

        validation_results.push(FileValidationResponse {
            filename: file_name,
            is_valid: validation_result.is_valid,
            file_size: validation_result.file_size,
            detected_mime_type: validation_result.detected_mime_type,
            security_issues: validation_result.security_issues
                .into_iter()
                .map(convert_security_issue_to_response)
                .collect(),
        });
    }

    Ok(Json(validation_results))
}

#[derive(Debug, Serialize)]
pub struct FileValidationResponse {
    pub filename: String,
    pub is_valid: bool,
    pub file_size: u64,
    pub detected_mime_type: String,
    pub security_issues: Vec<SecurityIssueResponse>,
}

/// Convert attachment entity to response
fn convert_attachment_to_response(attachment: Attachment) -> AttachmentResponse {
    AttachmentResponse {
        id: attachment.id,
        entity_type: attachment.entity_type,
        entity_id: attachment.entity_id,
        file_name: attachment.file_name,
        original_name: attachment.original_name,
        file_size: attachment.file_size,
        mime_type: attachment.mime_type,
        category: attachment.category,
        description: attachment.description,
        is_public: attachment.is_public,
        download_count: attachment.download_count,
        created_date: attachment.created_date,
    }
}

/// Convert security issue to response
fn convert_security_issue_to_response(issue: SecurityIssue) -> SecurityIssueResponse {
    SecurityIssueResponse {
        severity: match issue.severity {
            SecuritySeverity::Low => "low".to_string(),
            SecuritySeverity::Medium => "medium".to_string(),
            SecuritySeverity::High => "high".to_string(),
            SecuritySeverity::Critical => "critical".to_string(),
        },
        issue_type: issue.issue_type,
        description: issue.description,
        recommendation: issue.recommendation,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_convert_security_issue_to_response() {
        let issue = SecurityIssue {
            severity: SecuritySeverity::High,
            issue_type: "test_issue".to_string(),
            description: "Test description".to_string(),
            recommendation: "Test recommendation".to_string(),
        };

        let response = convert_security_issue_to_response(issue);
        assert_eq!(response.severity, "high");
        assert_eq!(response.issue_type, "test_issue");
    }
}
