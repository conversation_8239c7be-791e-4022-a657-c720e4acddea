use std::sync::Arc;
use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
    Extension,
};
use serde::{Deserialize, Serialize};

use crate::infrastructure::database::{
    DatabaseOptimizationService, DatabaseHealthStatus, DatabaseMetrics, SlowQuery
};
use crate::error::{AppError, AppResult};
use crate::presentation::handlers::audit_handler::CurrentUser;

/// Application state for database handlers
#[derive(Clone)]
pub struct DatabaseHandlerState {
    pub optimization_service: Arc<DatabaseOptimizationService>,
}

/// Query parameters for slow queries
#[derive(Debug, Deserialize)]
pub struct SlowQueryParams {
    pub limit: Option<i32>,
}

/// Response for database health check
#[derive(Debug, Serialize)]
pub struct DatabaseHealthResponse {
    pub status: String,
    pub metrics: DatabaseMetricsResponse,
    pub issues: Vec<String>,
    pub warnings: Vec<String>,
    pub recommendations: Vec<String>,
}

/// Database metrics response
#[derive(Debug, Serialize)]
pub struct DatabaseMetricsResponse {
    pub active_connections: u32,
    pub idle_connections: u32,
    pub total_connections: u32,
    pub connection_utilization_percent: f64,
    pub slow_queries_count: u64,
    pub average_query_time_ms: f64,
    pub cache_hit_ratio: f64,
    pub deadlocks_count: u64,
    pub largest_tables: Vec<TableSizeResponse>,
    pub most_used_indexes: Vec<IndexUsageResponse>,
}

#[derive(Debug, Serialize)]
pub struct TableSizeResponse {
    pub table_name: String,
    pub size_mb: f64,
    pub row_count: i64,
}

#[derive(Debug, Serialize)]
pub struct IndexUsageResponse {
    pub table_name: String,
    pub index_name: String,
    pub scans: i64,
    pub efficiency_ratio: f64,
}

#[derive(Debug, Serialize)]
pub struct SlowQueryResponse {
    pub query: String,
    pub duration_ms: f64,
    pub calls: i64,
    pub mean_time_ms: f64,
    pub rows: i64,
    pub efficiency_score: f64,
}

/// Get database health status and metrics
pub async fn get_database_health(
    State(state): State<DatabaseHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
) -> AppResult<Json<DatabaseHealthResponse>> {
    // Check permissions - only admins can view database health
    if !current_user.is_admin || !current_user.permissions.contains(&"database:read".to_string()) {
        return Err(AppError::permission_denied("database:read"));
    }

    let health_status = state.optimization_service
        .get_health_status()
        .await
        .map_err(|e| AppError::internal(format!("Failed to get database health: {}", e)))?;

    let response = convert_health_status_to_response(health_status);
    Ok(Json(response))
}

/// Get slow queries analysis
pub async fn get_slow_queries(
    State(state): State<DatabaseHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Query(params): Query<SlowQueryParams>,
) -> AppResult<Json<Vec<SlowQueryResponse>>> {
    // Check permissions
    if !current_user.is_admin || !current_user.permissions.contains(&"database:read".to_string()) {
        return Err(AppError::permission_denied("database:read"));
    }

    let limit = params.limit.unwrap_or(20).min(100); // Max 100 queries
    
    let slow_queries = state.optimization_service
        .performance_monitor
        .get_slow_queries(limit)
        .await
        .map_err(|e| AppError::internal(format!("Failed to get slow queries: {}", e)))?;

    let response: Vec<SlowQueryResponse> = slow_queries
        .into_iter()
        .map(|query| SlowQueryResponse {
            efficiency_score: calculate_query_efficiency(&query),
            query: query.query,
            duration_ms: query.duration_ms,
            calls: query.calls,
            mean_time_ms: query.mean_time_ms,
            rows: query.rows,
        })
        .collect();

    Ok(Json(response))
}

/// Trigger database optimization manually
pub async fn trigger_optimization(
    State(state): State<DatabaseHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
) -> AppResult<Json<serde_json::Value>> {
    // Check permissions - only admins can trigger optimization
    if !current_user.is_admin || !current_user.permissions.contains(&"database:optimize".to_string()) {
        return Err(AppError::permission_denied("database:optimize"));
    }

    state.optimization_service
        .force_optimization()
        .await
        .map_err(|e| AppError::internal(format!("Failed to run optimization: {}", e)))?;

    Ok(Json(serde_json::json!({
        "message": "Database optimization completed successfully",
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

/// Get database performance recommendations
pub async fn get_performance_recommendations(
    State(state): State<DatabaseHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
) -> AppResult<Json<Vec<String>>> {
    // Check permissions
    if !current_user.is_admin || !current_user.permissions.contains(&"database:read".to_string()) {
        return Err(AppError::permission_denied("database:read"));
    }

    let recommendations = state.optimization_service
        .performance_monitor
        .analyze_query_performance()
        .await
        .map_err(|e| AppError::internal(format!("Failed to analyze performance: {}", e)))?;

    Ok(Json(recommendations))
}

/// Convert health status to API response
fn convert_health_status_to_response(health_status: DatabaseHealthStatus) -> DatabaseHealthResponse {
    let metrics = &health_status.metrics;
    
    let connection_utilization = if metrics.total_connections > 0 {
        (metrics.active_connections as f64 / metrics.total_connections as f64) * 100.0
    } else {
        0.0
    };

    let largest_tables: Vec<TableSizeResponse> = metrics.table_sizes
        .iter()
        .take(10) // Top 10 largest tables
        .map(|table| TableSizeResponse {
            table_name: table.table_name.clone(),
            size_mb: table.size_bytes as f64 / (1024.0 * 1024.0),
            row_count: table.row_count,
        })
        .collect();

    let most_used_indexes: Vec<IndexUsageResponse> = metrics.index_usage
        .iter()
        .take(10) // Top 10 most used indexes
        .map(|index| IndexUsageResponse {
            table_name: index.table_name.clone(),
            index_name: index.index_name.clone(),
            scans: index.scans,
            efficiency_ratio: if index.tuples_read > 0 {
                index.tuples_fetched as f64 / index.tuples_read as f64
            } else {
                0.0
            },
        })
        .collect();

    let recommendations = generate_recommendations(&health_status);

    DatabaseHealthResponse {
        status: health_status.status,
        metrics: DatabaseMetricsResponse {
            active_connections: metrics.active_connections,
            idle_connections: metrics.idle_connections,
            total_connections: metrics.total_connections,
            connection_utilization_percent: connection_utilization,
            slow_queries_count: metrics.slow_queries_count,
            average_query_time_ms: metrics.average_query_time_ms,
            cache_hit_ratio: metrics.cache_hit_ratio,
            deadlocks_count: metrics.deadlocks_count,
            largest_tables,
            most_used_indexes,
        },
        issues: health_status.issues,
        warnings: health_status.warnings,
        recommendations,
    }
}

/// Generate performance recommendations based on health status
fn generate_recommendations(health_status: &DatabaseHealthStatus) -> Vec<String> {
    let mut recommendations = Vec::new();
    let metrics = &health_status.metrics;

    // Cache hit ratio recommendations
    if metrics.cache_hit_ratio < 95.0 {
        recommendations.push("Consider increasing shared_buffers to improve cache hit ratio".to_string());
    }

    // Connection pool recommendations
    let connection_utilization = metrics.active_connections as f64 / metrics.total_connections as f64;
    if connection_utilization > 0.8 {
        recommendations.push("Consider increasing connection pool size or optimizing connection usage".to_string());
    }

    // Slow query recommendations
    if metrics.slow_queries_count > 5 {
        recommendations.push("Review and optimize slow queries, consider adding indexes".to_string());
    }

    // Table size recommendations
    if let Some(largest_table) = metrics.table_sizes.first() {
        if largest_table.size_bytes > 1_000_000_000 { // > 1GB
            recommendations.push(format!(
                "Consider partitioning large table '{}' ({:.1} MB)",
                largest_table.table_name,
                largest_table.size_bytes as f64 / (1024.0 * 1024.0)
            ));
        }
    }

    // Index usage recommendations
    let unused_indexes = metrics.index_usage
        .iter()
        .filter(|index| index.scans == 0)
        .count();
    
    if unused_indexes > 0 {
        recommendations.push(format!("Consider dropping {} unused indexes to improve write performance", unused_indexes));
    }

    // Deadlock recommendations
    if metrics.deadlocks_count > 0 {
        recommendations.push("Review application logic to reduce deadlocks".to_string());
    }

    recommendations
}

/// Calculate query efficiency score (0-100)
fn calculate_query_efficiency(query: &SlowQuery) -> f64 {
    // Simple efficiency calculation based on rows returned vs time taken
    if query.mean_time_ms <= 0.0 {
        return 100.0;
    }

    let rows_per_ms = query.rows as f64 / query.mean_time_ms;
    
    // Normalize to 0-100 scale (arbitrary thresholds)
    if rows_per_ms >= 1000.0 {
        100.0
    } else if rows_per_ms >= 100.0 {
        80.0
    } else if rows_per_ms >= 10.0 {
        60.0
    } else if rows_per_ms >= 1.0 {
        40.0
    } else {
        20.0
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::infrastructure::database::performance::{TableSize, IndexUsage};

    #[test]
    fn test_calculate_query_efficiency() {
        let query = SlowQuery {
            query: "SELECT * FROM test".to_string(),
            duration_ms: 100.0,
            calls: 1,
            mean_time_ms: 10.0,
            rows: 100,
        };

        let efficiency = calculate_query_efficiency(&query);
        assert!(efficiency >= 0.0 && efficiency <= 100.0);
    }

    #[test]
    fn test_generate_recommendations() {
        let metrics = DatabaseMetrics {
            active_connections: 18,
            idle_connections: 2,
            total_connections: 20,
            slow_queries_count: 10,
            average_query_time_ms: 50.0,
            cache_hit_ratio: 85.0,
            deadlocks_count: 2,
            table_sizes: vec![TableSize {
                table_name: "large_table".to_string(),
                size_bytes: 2_000_000_000, // 2GB
                row_count: 1000000,
            }],
            index_usage: vec![IndexUsage {
                table_name: "test_table".to_string(),
                index_name: "unused_index".to_string(),
                scans: 0,
                tuples_read: 0,
                tuples_fetched: 0,
            }],
        };

        let health_status = DatabaseHealthStatus {
            status: "warning".to_string(),
            metrics,
            issues: vec![],
            warnings: vec![],
        };

        let recommendations = generate_recommendations(&health_status);
        
        assert!(!recommendations.is_empty());
        assert!(recommendations.iter().any(|r| r.contains("shared_buffers")));
        assert!(recommendations.iter().any(|r| r.contains("connection pool")));
        assert!(recommendations.iter().any(|r| r.contains("slow queries")));
    }
}
