use std::sync::Arc;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
    Extension,
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::domain::repositories::{AuditLogFilter, AuditStatistics};
use crate::domain::services::{AuditService, RequestContext};
use crate::domain::value_objects::{PaginationRequest, SearchQuery};
use crate::error::{AppError, AppResult};

/// Application state for audit handlers
#[derive(Clone)]
pub struct AuditHandlerState {
    pub audit_service: Arc<AuditService>,
}

/// Query parameters for audit log listing
#[derive(Debug, Deserialize)]
pub struct AuditLogQuery {
    // Pagination
    pub page: Option<u64>,
    pub limit: Option<u64>,
    
    // Search
    pub search: Option<String>,
    
    // Filters
    pub user_id: Option<Uuid>,
    pub entity_type: Option<String>,
    pub entity_id: Option<Uuid>,
    pub action: Option<String>,
    pub date_from: Option<DateTime<Utc>>,
    pub date_to: Option<DateTime<Utc>>,
    pub impersonated_by: Option<Uuid>,
}

/// Response for audit log listing
#[derive(Debug, Serialize)]
pub struct AuditLogListResponse {
    pub audit_logs: Vec<AuditLogResponse>,
    pub pagination: PaginationResponse,
}

/// Individual audit log response
#[derive(Debug, Serialize)]
pub struct AuditLogResponse {
    pub id: Uuid,
    pub tenant_id: Option<Uuid>,
    pub user_id: Option<Uuid>,
    pub impersonated_by: Option<Uuid>,
    pub action: String,
    pub entity_type: String,
    pub entity_id: Uuid,
    pub old_values: Option<serde_json::Value>,
    pub new_values: Option<serde_json::Value>,
    pub changes: Option<serde_json::Value>,
    pub request_id: Option<String>,
    pub session_id: Option<String>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub route: Option<String>,
    pub method: Option<String>,
    pub status_code: Option<i32>,
    pub created_date: DateTime<Utc>,
}

/// Pagination response
#[derive(Debug, Serialize)]
pub struct PaginationResponse {
    pub page: u64,
    pub limit: u64,
    pub total: u64,
    pub total_pages: u64,
}

/// Query parameters for audit statistics
#[derive(Debug, Deserialize)]
pub struct AuditStatisticsQuery {
    pub date_from: DateTime<Utc>,
    pub date_to: DateTime<Utc>,
}

/// Current user information (extracted from auth middleware)
#[derive(Debug, Clone)]
pub struct CurrentUser {
    pub user_id: Uuid,
    pub tenant_id: Uuid,
    pub is_admin: bool,
    pub permissions: Vec<String>,
}

/// Get audit logs with filtering and pagination
pub async fn get_audit_logs(
    State(state): State<AuditHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Query(query): Query<AuditLogQuery>,
) -> AppResult<Json<AuditLogListResponse>> {
    // Check permissions
    if !current_user.permissions.contains(&"audit:read".to_string()) {
        return Err(AppError::permission_denied("audit:read"));
    }

    // Build pagination
    let pagination = PaginationRequest {
        page: Some(query.page.unwrap_or(1)),
        per_page: Some(query.limit.unwrap_or(50).min(100)), // Max 100 items per page
    };

    // Build search query
    let search = SearchQuery {
        query: query.search,
        filters: std::collections::HashMap::new(),
        sort_by: None,
        sort_order: crate::domain::value_objects::search::SortOrder::Asc,
    };

    // Build filter - non-admin users can only see their tenant's logs
    let filter = AuditLogFilter {
        tenant_id: if current_user.is_admin {
            None // Admin can see all tenants
        } else {
            Some(current_user.tenant_id)
        },
        user_id: query.user_id,
        entity_type: query.entity_type,
        entity_id: query.entity_id,
        action: query.action,
        date_from: query.date_from,
        date_to: query.date_to,
        impersonated_by: query.impersonated_by,
    };

    let (audit_logs, pagination_result) = state.audit_service
        .get_audit_logs(filter, pagination, search)
        .await?;

    let audit_log_responses: Vec<AuditLogResponse> = audit_logs
        .into_iter()
        .map(|log| AuditLogResponse {
            id: log.id,
            tenant_id: log.tenant_id,
            user_id: log.user_id,
            impersonated_by: log.impersonated_by,
            action: log.action,
            entity_type: log.entity_type,
            entity_id: log.entity_id,
            old_values: log.old_values,
            new_values: log.new_values,
            changes: log.changes,
            request_id: log.request_id,
            session_id: log.session_id,
            ip_address: log.ip_address,
            user_agent: log.user_agent,
            route: log.route,
            method: log.method,
            status_code: log.status_code,
            created_date: log.created_date,
        })
        .collect();

    let response = AuditLogListResponse {
        audit_logs: audit_log_responses,
        pagination: PaginationResponse {
            page: pagination_result.page as u64,
            limit: pagination_result.limit(),
            total: pagination_result.total,
            total_pages: pagination_result.total_pages as u64,
        },
    };

    Ok(Json(response))
}

/// Get audit logs for a specific entity
pub async fn get_entity_audit_logs(
    State(state): State<AuditHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Path((entity_type, entity_id)): Path<(String, Uuid)>,
    Query(query): Query<AuditLogQuery>,
) -> AppResult<Json<AuditLogListResponse>> {
    // Check permissions
    if !current_user.permissions.contains(&"audit:read".to_string()) {
        return Err(AppError::permission_denied("audit:read"));
    }

    // Build pagination
    let pagination = PaginationRequest {
        page: Some(query.page.unwrap_or(1)),
        per_page: Some(query.limit.unwrap_or(50).min(100)),
    };

    let (audit_logs, pagination_result) = state.audit_service
        .get_entity_audit_logs(&entity_type, entity_id, pagination)
        .await?;

    // Filter by tenant if not admin
    let filtered_logs: Vec<_> = if current_user.is_admin {
        audit_logs
    } else {
        audit_logs
            .into_iter()
            .filter(|log| log.tenant_id == Some(current_user.tenant_id))
            .collect()
    };

    let audit_log_responses: Vec<AuditLogResponse> = filtered_logs
        .into_iter()
        .map(|log| AuditLogResponse {
            id: log.id,
            tenant_id: log.tenant_id,
            user_id: log.user_id,
            impersonated_by: log.impersonated_by,
            action: log.action,
            entity_type: log.entity_type,
            entity_id: log.entity_id,
            old_values: log.old_values,
            new_values: log.new_values,
            changes: log.changes,
            request_id: log.request_id,
            session_id: log.session_id,
            ip_address: log.ip_address,
            user_agent: log.user_agent,
            route: log.route,
            method: log.method,
            status_code: log.status_code,
            created_date: log.created_date,
        })
        .collect();

    let response = AuditLogListResponse {
        audit_logs: audit_log_responses,
        pagination: PaginationResponse {
            page: pagination_result.page as u64,
            limit: pagination_result.limit(),
            total: audit_log_responses.len() as u64,
            total_pages: ((audit_log_responses.len() as f64) / (pagination.per_page() as f64)).ceil() as u64,
        },
    };

    Ok(Json(response))
}

/// Get audit statistics
pub async fn get_audit_statistics(
    State(state): State<AuditHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Query(query): Query<AuditStatisticsQuery>,
) -> AppResult<Json<AuditStatistics>> {
    // Check permissions
    if !current_user.permissions.contains(&"audit:statistics".to_string()) {
        return Err(AppError::permission_denied("audit:statistics"));
    }

    let tenant_id = if current_user.is_admin {
        None // Admin can see all tenants
    } else {
        Some(current_user.tenant_id)
    };

    let statistics = state.audit_service
        .get_statistics(tenant_id, query.date_from, query.date_to)
        .await?;

    Ok(Json(statistics))
}

/// Get a specific audit log by ID
pub async fn get_audit_log(
    State(state): State<AuditHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Path(audit_log_id): Path<Uuid>,
) -> AppResult<Json<AuditLogResponse>> {
    // Check permissions
    if !current_user.permissions.contains(&"audit:read".to_string()) {
        return Err(AppError::permission_denied("audit:read"));
    }

    // This would need to be implemented in the audit service
    // For now, we'll return a not implemented error
    Err(AppError::internal("Get single audit log not yet implemented"))
}

/// Clean up old audit logs (admin only)
pub async fn cleanup_audit_logs(
    State(state): State<AuditHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Query(retention_days): Query<u32>,
) -> AppResult<Json<serde_json::Value>> {
    // Check permissions - only admins can cleanup
    if !current_user.is_admin || !current_user.permissions.contains(&"audit:cleanup".to_string()) {
        return Err(AppError::permission_denied("audit:cleanup"));
    }

    let deleted_count = state.audit_service
        .cleanup_old_logs(retention_days)
        .await?;

    Ok(Json(serde_json::json!({
        "deleted_count": deleted_count,
        "retention_days": retention_days
    })))
}
